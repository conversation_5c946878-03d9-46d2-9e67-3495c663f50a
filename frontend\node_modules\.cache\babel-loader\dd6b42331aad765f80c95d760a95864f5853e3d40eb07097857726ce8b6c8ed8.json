{"ast": null, "code": "export { default } from \"./BottomNavigation.js\";\nexport { default as bottomNavigationClasses } from \"./bottomNavigationClasses.js\";\nexport * from \"./bottomNavigationClasses.js\";", "map": {"version": 3, "names": ["default", "bottomNavigationClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/BottomNavigation/index.js"], "sourcesContent": ["export { default } from \"./BottomNavigation.js\";\nexport { default as bottomNavigationClasses } from \"./bottomNavigationClasses.js\";\nexport * from \"./bottomNavigationClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,8BAA8B;AACjF,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}