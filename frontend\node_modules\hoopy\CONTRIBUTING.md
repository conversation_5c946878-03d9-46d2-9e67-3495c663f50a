# Contribution guidelines

* Install/update the dependencies:
  ```
  npm i
  ```

* Make sure all of the tests pass:
  ```
  npm t
  ```

* Make sure the code lints:
  ```
  npm run lint
  ```

* Adhere to the coding conventions
  that are used elsewhere in the codebase.

* New code must have new unit tests.

* New features
  or changes to existing features
  must be documented in the [readme file](README.md).

* Add yourself to the [authors file](AUTHORS).

