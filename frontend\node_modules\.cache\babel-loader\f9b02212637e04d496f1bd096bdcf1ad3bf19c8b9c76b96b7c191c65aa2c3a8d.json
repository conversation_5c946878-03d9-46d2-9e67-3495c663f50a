{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Transactions from './pages/Transactions';\nimport Analytics from './pages/Analytics';\nimport Companies from './pages/Companies';\nimport './App.css';\n\n// Create Material-UI theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600\n    },\n    h5: {\n      fontWeight: 600\n    },\n    h6: {\n      fontWeight: 600\n    }\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 8\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 6\n        }\n      }\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDateFns,\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/transactions\",\n              element: /*#__PURE__*/_jsxDEV(Transactions, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/analytics\",\n              element: /*#__PURE__*/_jsxDEV(Analytics, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/companies\",\n              element: /*#__PURE__*/_jsxDEV(Companies, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ThemeProvider", "createTheme", "CssBaseline", "LocalizationProvider", "AdapterDateFns", "Layout", "Dashboard", "Transactions", "Analytics", "Companies", "jsxDEV", "_jsxDEV", "theme", "palette", "mode", "primary", "main", "secondary", "background", "default", "typography", "fontFamily", "h4", "fontWeight", "h5", "h6", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "borderRadius", "MuiB<PERSON>on", "textTransform", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dateAdapter", "path", "element", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\nimport { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\nimport Layout from './components/Layout';\nimport Dashboard from './pages/Dashboard';\nimport Transactions from './pages/Transactions';\nimport Analytics from './pages/Analytics';\nimport Companies from './pages/Companies';\nimport './App.css';\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h4: {\n      fontWeight: 600,\n    },\n    h5: {\n      fontWeight: 600,\n    },\n    h6: {\n      fontWeight: 600,\n    },\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          borderRadius: 8,\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          borderRadius: 6,\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <LocalizationProvider dateAdapter={AdapterDateFns}>\n        <Router>\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/transactions\" element={<Transactions />} />\n              <Route path=\"/analytics\" element={<Analytics />} />\n              <Route path=\"/companies\" element={<Companies />} />\n            </Routes>\n          </Layout>\n        </Router>\n      </LocalizationProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,cAAc,QAAQ,oCAAoC;AACnE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGX,WAAW,CAAC;EACxBY,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,4CAA4C;IACxDC,EAAE,EAAE;MACFC,UAAU,EAAE;IACd,CAAC;IACDC,EAAE,EAAE;MACFD,UAAU,EAAE;IACd,CAAC;IACDE,EAAE,EAAE;MACFF,UAAU,EAAE;IACd;EACF,CAAC;EACDG,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE,2BAA2B;UACtCC,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTJ,cAAc,EAAE;QACdC,IAAI,EAAE;UACJI,aAAa,EAAE,MAAM;UACrBF,YAAY,EAAE;QAChB;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,SAASG,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACX,aAAa;IAACY,KAAK,EAAEA,KAAM;IAAAuB,QAAA,gBAC1BxB,OAAA,CAACT,WAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf5B,OAAA,CAACR,oBAAoB;MAACqC,WAAW,EAAEpC,cAAe;MAAA+B,QAAA,eAChDxB,OAAA,CAACd,MAAM;QAAAsC,QAAA,eACLxB,OAAA,CAACN,MAAM;UAAA8B,QAAA,eACLxB,OAAA,CAACb,MAAM;YAAAqC,QAAA,gBACLxB,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,GAAG;cAACC,OAAO,eAAE/B,OAAA,CAACL,SAAS;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1C5B,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,eAAe;cAACC,OAAO,eAAE/B,OAAA,CAACJ,YAAY;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzD5B,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,YAAY;cAACC,OAAO,eAAE/B,OAAA,CAACH,SAAS;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnD5B,OAAA,CAACZ,KAAK;cAAC0C,IAAI,EAAC,YAAY;cAACC,OAAO,eAAE/B,OAAA,CAACF,SAAS;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEpB;AAACI,EAAA,GAlBQT,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}