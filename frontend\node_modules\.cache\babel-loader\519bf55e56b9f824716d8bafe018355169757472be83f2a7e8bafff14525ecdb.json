{"ast": null, "code": "'use client';\n\nimport ClassNameGenerator from '@mui/utils/ClassNameGenerator';\nexport { default as capitalize } from \"./capitalize.js\";\nexport { default as createChainedFunction } from \"./createChainedFunction.js\";\nexport { default as createSvgIcon } from \"./createSvgIcon.js\";\nexport { default as debounce } from \"./debounce.js\";\nexport { default as deprecatedPropType } from \"./deprecatedPropType.js\";\nexport { default as isMuiElement } from \"./isMuiElement.js\";\nexport { default as unstable_memoTheme } from \"./memoTheme.js\";\nexport { default as ownerDocument } from \"./ownerDocument.js\";\nexport { default as ownerWindow } from \"./ownerWindow.js\";\nexport { default as requirePropFactory } from \"./requirePropFactory.js\";\nexport { default as setRef } from \"./setRef.js\";\nexport { default as unstable_useEnhancedEffect } from \"./useEnhancedEffect.js\";\nexport { default as unstable_useId } from \"./useId.js\";\nexport { default as unsupportedProp } from \"./unsupportedProp.js\";\nexport { default as useControlled } from \"./useControlled.js\";\nexport { default as useEventCallback } from \"./useEventCallback.js\";\nexport { default as useForkRef } from \"./useForkRef.js\";\nexport { default as mergeSlotProps } from \"./mergeSlotProps.js\";\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};", "map": {"version": 3, "names": ["ClassNameGenerator", "default", "capitalize", "createChainedFunction", "createSvgIcon", "debounce", "deprecatedPropType", "isMuiElement", "unstable_memoTheme", "ownerDocument", "ownerWindow", "requirePropFactory", "setRef", "unstable_useEnhancedEffect", "unstable_useId", "unsupportedProp", "useControlled", "useEventCallback", "useForkRef", "mergeSlotProps", "unstable_ClassNameGenerator", "configure", "generator", "process", "env", "NODE_ENV", "console", "warn", "join"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/utils/index.js"], "sourcesContent": ["'use client';\n\nimport ClassNameGenerator from '@mui/utils/ClassNameGenerator';\nexport { default as capitalize } from \"./capitalize.js\";\nexport { default as createChainedFunction } from \"./createChainedFunction.js\";\nexport { default as createSvgIcon } from \"./createSvgIcon.js\";\nexport { default as debounce } from \"./debounce.js\";\nexport { default as deprecatedPropType } from \"./deprecatedPropType.js\";\nexport { default as isMuiElement } from \"./isMuiElement.js\";\nexport { default as unstable_memoTheme } from \"./memoTheme.js\";\nexport { default as ownerDocument } from \"./ownerDocument.js\";\nexport { default as ownerWindow } from \"./ownerWindow.js\";\nexport { default as requirePropFactory } from \"./requirePropFactory.js\";\nexport { default as setRef } from \"./setRef.js\";\nexport { default as unstable_useEnhancedEffect } from \"./useEnhancedEffect.js\";\nexport { default as unstable_useId } from \"./useId.js\";\nexport { default as unsupportedProp } from \"./unsupportedProp.js\";\nexport { default as useControlled } from \"./useControlled.js\";\nexport { default as useEventCallback } from \"./useEventCallback.js\";\nexport { default as useForkRef } from \"./useForkRef.js\";\nexport { default as mergeSlotProps } from \"./mergeSlotProps.js\";\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,OAAO,IAAIC,UAAU,QAAQ,iBAAiB;AACvD,SAASD,OAAO,IAAIE,qBAAqB,QAAQ,4BAA4B;AAC7E,SAASF,OAAO,IAAIG,aAAa,QAAQ,oBAAoB;AAC7D,SAASH,OAAO,IAAII,QAAQ,QAAQ,eAAe;AACnD,SAASJ,OAAO,IAAIK,kBAAkB,QAAQ,yBAAyB;AACvE,SAASL,OAAO,IAAIM,YAAY,QAAQ,mBAAmB;AAC3D,SAASN,OAAO,IAAIO,kBAAkB,QAAQ,gBAAgB;AAC9D,SAASP,OAAO,IAAIQ,aAAa,QAAQ,oBAAoB;AAC7D,SAASR,OAAO,IAAIS,WAAW,QAAQ,kBAAkB;AACzD,SAAST,OAAO,IAAIU,kBAAkB,QAAQ,yBAAyB;AACvE,SAASV,OAAO,IAAIW,MAAM,QAAQ,aAAa;AAC/C,SAASX,OAAO,IAAIY,0BAA0B,QAAQ,wBAAwB;AAC9E,SAASZ,OAAO,IAAIa,cAAc,QAAQ,YAAY;AACtD,SAASb,OAAO,IAAIc,eAAe,QAAQ,sBAAsB;AACjE,SAASd,OAAO,IAAIe,aAAa,QAAQ,oBAAoB;AAC7D,SAASf,OAAO,IAAIgB,gBAAgB,QAAQ,uBAAuB;AACnE,SAAShB,OAAO,IAAIiB,UAAU,QAAQ,iBAAiB;AACvD,SAASjB,OAAO,IAAIkB,cAAc,QAAQ,qBAAqB;AAC/D;AACA;AACA,OAAO,MAAMC,2BAA2B,GAAG;EACzCC,SAAS,EAAEC,SAAS,IAAI;IACtB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCC,OAAO,CAACC,IAAI,CAAC,CAAC,4GAA4G,EAAE,EAAE,EAAE,gGAAgG,EAAE,EAAE,EAAE,kGAAkG,EAAE,EAAE,EAAE,wEAAwE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACra;IACA5B,kBAAkB,CAACqB,SAAS,CAACC,SAAS,CAAC;EACzC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}