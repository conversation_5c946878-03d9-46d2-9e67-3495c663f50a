import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Typography,
  Box,
  Link,
} from '@mui/material';
import { format } from 'date-fns';
import { Transaction } from '../services/apiService';

interface RecentTransactionsProps {
  transactions: Transaction[];
}

const RecentTransactions: React.FC<RecentTransactionsProps> = ({ transactions }) => {
  const formatCurrency = (value: number | undefined | null) => {
    if (!value || value === 0) return '-';
    if (value >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (value >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  const getTransactionTypeColor = (type: string | undefined) => {
    if (!type) return 'default';
    const lowerType = type.toLowerCase();
    if (lowerType.includes('buy') || lowerType.includes('acquisition')) {
      return 'success';
    } else if (lowerType.includes('sell') || lowerType.includes('disposal')) {
      return 'error';
    } else {
      return 'info';
    }
  };

  const getPersonCategoryColor = (category: string | undefined) => {
    if (!category) return 'default';
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('promoter')) {
      return 'primary';
    } else if (lowerCategory.includes('director')) {
      return 'secondary';
    } else if (lowerCategory.includes('employee')) {
      return 'info';
    } else {
      return 'default';
    }
  };

  if (!transactions || transactions.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography variant="body1" color="text.secondary">
          No recent transactions found
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer component={Paper} elevation={0}>
      <Table sx={{ minWidth: 650 }} aria-label="recent transactions table">
        <TableHead>
          <TableRow>
            <TableCell>
              <Typography variant="subtitle2" fontWeight={600}>
                Company
              </Typography>
            </TableCell>
            <TableCell>
              <Typography variant="subtitle2" fontWeight={600}>
                Person
              </Typography>
            </TableCell>
            <TableCell>
              <Typography variant="subtitle2" fontWeight={600}>
                Category
              </Typography>
            </TableCell>
            <TableCell>
              <Typography variant="subtitle2" fontWeight={600}>
                Transaction Type
              </Typography>
            </TableCell>
            <TableCell align="right">
              <Typography variant="subtitle2" fontWeight={600}>
                Value
              </Typography>
            </TableCell>
            <TableCell>
              <Typography variant="subtitle2" fontWeight={600}>
                Date
              </Typography>
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {transactions.map((transaction) => (
            <TableRow
              key={transaction.id}
              sx={{
                '&:last-child td, &:last-child th': { border: 0 },
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight={600}>
                    {transaction.symbol}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      display: 'block',
                      maxWidth: 200,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {transaction.company_name}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  sx={{
                    maxWidth: 150,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {transaction.person_name}
                </Typography>
              </TableCell>
              <TableCell>
                {transaction.person_category && (
                  <Chip
                    label={transaction.person_category}
                    size="small"
                    color={getPersonCategoryColor(transaction.person_category) as any}
                    variant="outlined"
                  />
                )}
              </TableCell>
              <TableCell>
                {transaction.transaction_type && (
                  <Chip
                    label={transaction.transaction_type}
                    size="small"
                    color={getTransactionTypeColor(transaction.transaction_type) as any}
                  />
                )}
              </TableCell>
              <TableCell align="right">
                <Typography variant="body2" fontWeight={600}>
                  {formatCurrency(transaction.security_value)}
                </Typography>
                {transaction.securities_acquired && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    {transaction.securities_acquired.toLocaleString()} shares
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {formatDate(transaction.transaction_date)}
                </Typography>
                {transaction.intimation_date && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    Intimated: {formatDate(transaction.intimation_date)}
                  </Typography>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default RecentTransactions;
