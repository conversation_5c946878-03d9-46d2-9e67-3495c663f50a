"""
Pydantic models for NSE Insider Trading API
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Base response models
class APIResponse(BaseModel):
    success: bool = True
    data: Optional[Any] = None
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Transaction models
class TransactionBase(BaseModel):
    symbol: str
    company_name: str
    person_name: str
    person_category: Optional[str] = None
    transaction_date: datetime
    intimation_date: Optional[datetime] = None
    transaction_type: Optional[str] = None
    transaction_mode: Optional[str] = None
    security_type: Optional[str] = None
    exchange_name: Optional[str] = None
    buy_value: Optional[float] = None
    sell_value: Optional[float] = None
    buy_quantity: Optional[int] = None
    sell_quantity: Optional[int] = None
    security_value: Optional[float] = None
    securities_acquired: Optional[int] = None
    shares_before_transaction: Optional[int] = None
    percentage_before_transaction: Optional[float] = None
    shares_after_transaction: Optional[int] = None
    percentage_after_transaction: Optional[float] = None
    remarks: Optional[str] = None
    xbrl_link: Optional[str] = None
    created_at: datetime

class Transaction(TransactionBase):
    id: str

class TransactionListResponse(BaseModel):
    transactions: List[Transaction]
    total_count: int
    page: int
    limit: int
    total_pages: int

# Analytics models
class TransactionSummary(BaseModel):
    total_transactions: int
    total_buy_value: Optional[float] = None
    total_sell_value: Optional[float] = None
    unique_companies: int
    unique_persons: int
    date_range: Dict[str, datetime]

class CompanyInsiderActivity(BaseModel):
    symbol: str
    company_name: str
    total_transactions: int
    total_buy_value: Optional[float] = None
    total_sell_value: Optional[float] = None
    net_value: Optional[float] = None
    unique_insiders: int
    latest_transaction_date: Optional[datetime] = None

class PersonInsiderActivity(BaseModel):
    person_name: str
    person_category: Optional[str] = None
    companies: List[str]
    total_transactions: int
    total_buy_value: Optional[float] = None
    total_sell_value: Optional[float] = None
    net_value: Optional[float] = None
    latest_transaction_date: Optional[datetime] = None

class TimeSeriesData(BaseModel):
    date: datetime
    buy_value: Optional[float] = None
    sell_value: Optional[float] = None
    transaction_count: int
    net_value: Optional[float] = None

class AnalyticsResponse(BaseModel):
    summary: TransactionSummary
    top_companies: List[CompanyInsiderActivity]
    top_persons: List[PersonInsiderActivity]
    time_series: List[TimeSeriesData]

# Filter models
class DateRange(BaseModel):
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None

class ValueRange(BaseModel):
    min_value: Optional[float] = None
    max_value: Optional[float] = None

class TransactionFilter(BaseModel):
    symbol: Optional[str] = None
    person_name: Optional[str] = None
    person_category: Optional[str] = None
    transaction_type: Optional[str] = None
    date_range: Optional[DateRange] = None
    value_range: Optional[ValueRange] = None

# Aggregation models
class AggregationPeriod(str, Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"

class AggregationRequest(BaseModel):
    period: AggregationPeriod
    filters: Optional[TransactionFilter] = None
    group_by: Optional[List[str]] = None

class AggregationResult(BaseModel):
    period: str
    period_start: datetime
    period_end: datetime
    metrics: Dict[str, Any]

class AggregationResponse(BaseModel):
    results: List[AggregationResult]
    total_periods: int
    aggregation_period: AggregationPeriod

# Search models
class SearchRequest(BaseModel):
    query: str = Field(..., min_length=2, max_length=100)
    search_type: Optional[str] = Field("all", pattern="^(all|company|person|transaction)$")
    limit: Optional[int] = Field(10, ge=1, le=100)

class SearchResult(BaseModel):
    type: str
    id: str
    title: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total_count: int
    query: str
    search_type: str

# System status models
class ScraperStatus(BaseModel):
    last_execution: Optional[datetime] = None
    status: str
    records_fetched: int = 0
    records_inserted: int = 0
    records_skipped: int = 0
    error_message: Optional[str] = None

class DatabaseStatus(BaseModel):
    total_transactions: int
    total_companies: int
    date_range: Optional[Dict[str, datetime]] = None
    last_updated: Optional[datetime] = None

class SystemStatus(BaseModel):
    api_status: str
    database_status: DatabaseStatus
    scraper_status: ScraperStatus
    cache_status: str
    timestamp: datetime

# Export models
class ExportFormat(str, Enum):
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"

class ExportRequest(BaseModel):
    format: ExportFormat
    filters: Optional[TransactionFilter] = None
    fields: Optional[List[str]] = None
    limit: Optional[int] = Field(None, le=10000)

class ExportResponse(BaseModel):
    download_url: str
    file_name: str
    format: ExportFormat
    record_count: int
    expires_at: datetime

# Notification models
class AlertType(str, Enum):
    UNUSUAL_ACTIVITY = "unusual_activity"
    HIGH_VALUE_TRANSACTION = "high_value_transaction"
    FREQUENT_TRADING = "frequent_trading"
    NEW_INSIDER = "new_insider"

class AlertRule(BaseModel):
    id: Optional[str] = None
    name: str
    alert_type: AlertType
    conditions: Dict[str, Any]
    is_active: bool = True
    created_at: Optional[datetime] = None

class Alert(BaseModel):
    id: str
    rule_id: str
    alert_type: AlertType
    title: str
    description: str
    data: Dict[str, Any]
    is_read: bool = False
    created_at: datetime

class AlertResponse(BaseModel):
    alerts: List[Alert]
    unread_count: int
    total_count: int

# Validation helpers
@validator('security_value', 'buy_value', 'sell_value', pre=True, always=True)
def validate_monetary_values(cls, v):
    """Validate monetary values"""
    if v is not None and v < 0:
        raise ValueError('Monetary values cannot be negative')
    return v

@validator('percentage_before_transaction', 'percentage_after_transaction', pre=True, always=True)
def validate_percentages(cls, v):
    """Validate percentage values"""
    if v is not None and (v < 0 or v > 100):
        raise ValueError('Percentage values must be between 0 and 100')
    return v

# Response wrapper
class APIResponse(BaseModel):
    success: bool = True
    data: Optional[Any] = None
    message: Optional[str] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
