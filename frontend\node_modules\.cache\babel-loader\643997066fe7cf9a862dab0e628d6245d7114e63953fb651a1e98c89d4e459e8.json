{"ast": null, "code": "export { default } from \"./DialogActions.js\";\nexport { default as dialogActionsClasses } from \"./dialogActionsClasses.js\";\nexport * from \"./dialogActionsClasses.js\";", "map": {"version": 3, "names": ["default", "dialogActionsClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/DialogActions/index.js"], "sourcesContent": ["export { default } from \"./DialogActions.js\";\nexport { default as dialogActionsClasses } from \"./dialogActionsClasses.js\";\nexport * from \"./dialogActionsClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASA,OAAO,IAAIC,oBAAoB,QAAQ,2BAA2B;AAC3E,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}