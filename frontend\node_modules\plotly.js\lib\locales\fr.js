'use strict';

module.exports = {
    moduleType: 'locale',
    name: 'fr',
    dictionary: {
        'Autoscale': 'Échelle automatique',
        'Box Select': 'Sélection rectangulaire',
        'Click to enter Colorscale title': 'Ajouter un titre à l\'échelle de couleurs',
        'Click to enter Component A title': 'Ajouter un titre à la composante A',
        'Click to enter Component B title': 'Ajouter un titre à la composante B',
        'Click to enter Component C title': 'Ajouter un titre à la composante C',
        'Click to enter Plot title': 'Ajouter un titre au graphique',
        'Click to enter Plot subtitle': 'Ajouter un sous-titre au graphique',
        'Click to enter X axis title': 'Ajouter un titre à l\'axe des x',
        'Click to enter Y axis title': 'Ajouter un titre à l\'axe des y',
        'Click to enter radial axis title': 'Ajouter un titre à l\'axe radial',
        'Compare data on hover': 'Comparaison entre données en survol',
        'Double-click on legend to isolate one trace': 'Double-cliquer sur la légende pour isoler une série',
        'Double-click to zoom back out': 'Double-cliquer pour dézoomer',
        'Download plot as a png': 'Télécharger le graphique en fichier PNG',
        'Download plot': 'Télécharger le graphique',
        'Edit in Chart Studio': 'Éditer le graphique sur Chart Studio',
        'IE only supports svg.  Changing format to svg.': 'IE ne permet que les conversions en SVG. Conversion en SVG en cours.',
        'Lasso Select': 'Sélection lasso',
        'Orbital rotation': 'Rotation orbitale',
        'Pan': 'Translation',
        'Produced with Plotly.js': 'Généré avec Plotly.js',
        'Reset': 'Réinitialiser',
        'Reset axes': 'Réinitialiser les axes',
        'Reset camera to default': 'Régler la caméra à sa valeur défaut',
        'Reset camera to last save': 'Régler la caméra à sa valeur sauvegardée',
        'Reset view': 'Réinitialiser',
        'Reset views': 'Réinitialiser',
        'Show closest data on hover': 'Données les plus proches en survol',
        'Snapshot succeeded': 'Conversion réussie',
        'Sorry, there was a problem downloading your snapshot!': 'Désolé, un problème est survenu lors du téléchargement de votre graphique',
        'Taking snapshot - this may take a few seconds': 'Conversion en cours, ceci peut prendre quelques secondes',
        'Zoom': 'Zoom',
        'Zoom in': 'Zoom intérieur',
        'Zoom out': 'Zoom extérieur',
        'close:': 'fermeture :',
        'trace': 'série',
        'lat:': 'lat. :',
        'lon:': 'lon. :',
        'q1:': 'q1 :',
        'q3:': 'q3 :',
        'source:': 'source :',
        'target:': 'embouchure :',
        'lower fence:': 'clôture inférieure :',
        'upper fence:': 'clôture supérieure :',
        'max:': 'max. :',
        'mean ± σ:': 'moyenne ± σ :',
        'mean:': 'moyenne :',
        'median:': 'médiane :',
        'min:': 'min. :',
        'new text': 'nouveau texte',
        'Turntable rotation': 'Rotation planaire',
        'Toggle Spike Lines': 'Activer/désactiver les pics',
        'open:': 'ouverture :',
        'high:': 'haut :',
        'low:': 'bas :',
        'Toggle show closest data on hover': 'Activer/désactiver le survol',
        'incoming flow count:': 'flux entrant :',
        'outgoing flow count:': 'flux sortant :',
        'kde:': 'est. par noyau :'
    },
    format: {
        days: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
        shortDays: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
        months: [
            'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
            'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ],
        shortMonths: [
            'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
            'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
        ],
        date: '%d/%m/%Y',
        decimal: ',',
        thousands: ' ',
        year: '%Y',
        month: '%b %Y',
        dayMonth: '%-d %b',
        dayMonthYear: '%-d %b %Y'
    }
};
