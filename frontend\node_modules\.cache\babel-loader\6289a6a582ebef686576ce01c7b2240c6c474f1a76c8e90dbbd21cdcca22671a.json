{"ast": null, "code": "export { default } from \"./Dialog.js\";\nexport { default as dialogClasses } from \"./dialogClasses.js\";\nexport * from \"./dialogClasses.js\";", "map": {"version": 3, "names": ["default", "dialogClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Dialog/index.js"], "sourcesContent": ["export { default } from \"./Dialog.js\";\nexport { default as dialogClasses } from \"./dialogClasses.js\";\nexport * from \"./dialogClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,aAAa,QAAQ,oBAAoB;AAC7D,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}