{"ast": null, "code": "export { default } from \"./Collapse.js\";\nexport { default as collapseClasses } from \"./collapseClasses.js\";\nexport * from \"./collapseClasses.js\";", "map": {"version": 3, "names": ["default", "collapseClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Collapse/index.js"], "sourcesContent": ["export { default } from \"./Collapse.js\";\nexport { default as collapseClasses } from \"./collapseClasses.js\";\nexport * from \"./collapseClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,eAAe,QAAQ,sBAAsB;AACjE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}