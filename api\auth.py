"""
Authentication and Authorization module for NSE Insider Trading API
"""

import jwt
import bcrypt
import secrets
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import logging
from pathlib import Path
import json
import uuid

logger = logging.getLogger(__name__)

class AuthManager:
    """Handles authentication and authorization"""
    
    def __init__(self, secret_key: Optional[str] = None):
        self.secret_key = secret_key or self._generate_secret_key()
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
        
        # In-memory user store (replace with database in production)
        self.users = self._load_users()
        self.refresh_tokens = {}  # Store active refresh tokens
    
    def _generate_secret_key(self) -> str:
        """Generate a secure secret key"""
        return secrets.token_urlsafe(32)
    
    def _load_users(self) -> Dict[str, Dict[str, Any]]:
        """Load users from configuration file"""
        users_file = Path("config/users.json")
        
        # Default admin user
        default_users = {
            "admin": {
                "id": str(uuid.uuid4()),
                "username": "admin",
                "email": "<EMAIL>",
                "password_hash": self._hash_password("admin123"),
                "is_active": True,
                "is_admin": True,
                "created_at": datetime.now(timezone.utc).isoformat()
            },
            "demo": {
                "id": str(uuid.uuid4()),
                "username": "demo",
                "email": "<EMAIL>",
                "password_hash": self._hash_password("demo123"),
                "is_active": True,
                "is_admin": False,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
        }
        
        try:
            if users_file.exists():
                with open(users_file, 'r') as f:
                    users = json.load(f)
                    # Merge with defaults if needed
                    for username, user_data in default_users.items():
                        if username not in users:
                            users[username] = user_data
                    return users
            else:
                # Create users file with defaults
                users_file.parent.mkdir(exist_ok=True)
                with open(users_file, 'w') as f:
                    json.dump(default_users, f, indent=2)
                logger.info(f"Created default users file: {users_file}")
                return default_users
                
        except Exception as e:
            logger.error(f"Failed to load users, using defaults: {e}")
            return default_users
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            logger.error(f"Password verification failed: {e}")
            return False
    
    def _create_access_token(self, user_data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        expire = datetime.now(timezone.utc) + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": user_data["username"],
            "user_id": user_data["id"],
            "email": user_data.get("email"),
            "is_admin": user_data.get("is_admin", False),
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "type": "access"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _create_refresh_token(self, user_data: Dict[str, Any]) -> str:
        """Create JWT refresh token"""
        expire = datetime.now(timezone.utc) + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "sub": user_data["username"],
            "user_id": user_data["id"],
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "type": "refresh"
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        # Store refresh token
        self.refresh_tokens[token] = {
            "user_id": user_data["id"],
            "username": user_data["username"],
            "created_at": datetime.now(timezone.utc),
            "expires_at": expire
        }
        
        return token
    
    async def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return tokens"""
        try:
            # Find user
            user_data = self.users.get(username)
            if not user_data:
                raise ValueError("Invalid credentials")
            
            # Check if user is active
            if not user_data.get("is_active", False):
                raise ValueError("User account is disabled")
            
            # Verify password
            if not self._verify_password(password, user_data["password_hash"]):
                raise ValueError("Invalid credentials")
            
            # Create tokens
            access_token = self._create_access_token(user_data)
            refresh_token = self._create_refresh_token(user_data)
            
            logger.info(f"User {username} authenticated successfully")
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": self.access_token_expire_minutes * 60
            }
            
        except Exception as e:
            logger.warning(f"Authentication failed for user {username}: {e}")
            raise
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token and return user data"""
        try:
            # Decode token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != "access":
                raise ValueError("Invalid token type")
            
            # Get user data
            username = payload.get("sub")
            user_data = self.users.get(username)
            
            if not user_data:
                raise ValueError("User not found")
            
            if not user_data.get("is_active", False):
                raise ValueError("User account is disabled")
            
            return {
                "id": user_data["id"],
                "username": user_data["username"],
                "email": user_data.get("email"),
                "is_admin": user_data.get("is_admin", False),
                "is_active": user_data.get("is_active", True)
            }
            
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid token: {e}")
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise ValueError("Token verification failed")
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        try:
            # Check if refresh token exists
            if refresh_token not in self.refresh_tokens:
                raise ValueError("Invalid refresh token")
            
            # Decode refresh token
            payload = jwt.decode(refresh_token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != "refresh":
                raise ValueError("Invalid token type")
            
            # Get user data
            username = payload.get("sub")
            user_data = self.users.get(username)
            
            if not user_data:
                raise ValueError("User not found")
            
            if not user_data.get("is_active", False):
                raise ValueError("User account is disabled")
            
            # Create new tokens
            new_access_token = self._create_access_token(user_data)
            new_refresh_token = self._create_refresh_token(user_data)
            
            # Remove old refresh token
            del self.refresh_tokens[refresh_token]
            
            logger.info(f"Tokens refreshed for user {username}")
            
            return {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer",
                "expires_in": self.access_token_expire_minutes * 60
            }
            
        except jwt.ExpiredSignatureError:
            # Clean up expired refresh token
            if refresh_token in self.refresh_tokens:
                del self.refresh_tokens[refresh_token]
            raise ValueError("Refresh token has expired")
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid refresh token: {e}")
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise ValueError("Token refresh failed")
    
    async def revoke_refresh_token(self, refresh_token: str) -> bool:
        """Revoke a refresh token"""
        try:
            if refresh_token in self.refresh_tokens:
                del self.refresh_tokens[refresh_token]
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to revoke refresh token: {e}")
            return False
    
    def cleanup_expired_tokens(self):
        """Clean up expired refresh tokens"""
        try:
            now = datetime.now(timezone.utc)
            expired_tokens = []
            
            for token, token_data in self.refresh_tokens.items():
                if token_data["expires_at"] < now:
                    expired_tokens.append(token)
            
            for token in expired_tokens:
                del self.refresh_tokens[token]
            
            if expired_tokens:
                logger.info(f"Cleaned up {len(expired_tokens)} expired refresh tokens")
                
        except Exception as e:
            logger.error(f"Failed to cleanup expired tokens: {e}")
    
    async def create_user(self, username: str, password: str, email: Optional[str] = None, is_admin: bool = False) -> Dict[str, Any]:
        """Create a new user (admin only)"""
        try:
            if username in self.users:
                raise ValueError("Username already exists")
            
            user_data = {
                "id": str(uuid.uuid4()),
                "username": username,
                "email": email,
                "password_hash": self._hash_password(password),
                "is_active": True,
                "is_admin": is_admin,
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.users[username] = user_data
            
            # Save to file
            users_file = Path("config/users.json")
            with open(users_file, 'w') as f:
                json.dump(self.users, f, indent=2)
            
            logger.info(f"Created new user: {username}")
            
            return {
                "id": user_data["id"],
                "username": user_data["username"],
                "email": user_data["email"],
                "is_admin": user_data["is_admin"],
                "is_active": user_data["is_active"],
                "created_at": user_data["created_at"]
            }
            
        except Exception as e:
            logger.error(f"Failed to create user {username}: {e}")
            raise
