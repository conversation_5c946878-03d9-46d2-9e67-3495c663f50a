import { IUtils, DateIOFormats, Unit } from "@date-io/core/IUtils";
import { enUS as defaultLocale } from "date-fns/locale/en-US";
type Locale = typeof defaultLocale;
export default class DateFnsUtils implements IUtils<Date, Locale> {
    lib: string;
    locale?: Locale;
    formats: DateIOFormats;
    constructor({ locale, formats, }?: {
        formats?: Partial<DateIOFormats>;
        locale?: Locale;
        instance?: any;
    });
    is12HourCycleInCurrentLocale: () => boolean;
    getFormatHelperText: (format: string) => string;
    parseISO: (isoString: string) => Date;
    toISO: (value: Date) => string;
    getCurrentLocaleCode: () => string;
    addSeconds: (value: Date, count: number) => Date;
    addMinutes: (value: Date, count: number) => Date;
    addHours: (value: Date, count: number) => Date;
    addDays: (value: Date, count: number) => Date;
    addWeeks: (value: Date, count: number) => Date;
    addMonths: (value: Date, count: number) => Date;
    addYears: (value: Date, count: number) => Date;
    isValid: (value: any) => boolean;
    getDiff: (value: Date, comparing: Date | string, unit?: Unit) => number;
    isAfter: (value: Date, comparing: Date) => boolean;
    isBefore: (value: Date, comparing: Date) => boolean;
    startOfDay: (value: Date) => Date;
    endOfDay: (value: Date) => Date;
    getHours: (value: Date) => number;
    setHours: (value: Date, count: number) => Date;
    setMinutes: (value: Date, count: number) => Date;
    getSeconds: (value: Date) => number;
    setSeconds: (value: Date, count: number) => Date;
    isSameDay: (value: Date, comparing: Date) => boolean;
    isSameMonth: (value: Date, comparing: Date) => boolean;
    isSameYear: (value: Date, comparing: Date) => boolean;
    isSameHour: (value: Date, comparing: Date) => boolean;
    startOfYear: (value: Date) => Date;
    endOfYear: (value: Date) => Date;
    startOfMonth: (value: Date) => Date;
    endOfMonth: (value: Date) => Date;
    startOfWeek: (value: Date) => Date;
    endOfWeek: (value: Date) => Date;
    getYear: (value: Date) => number;
    setYear: (value: Date, count: number) => Date;
    date<TArg extends unknown = undefined, TRes extends unknown = TArg extends null ? null : TArg extends undefined ? Date : Date | null>(value?: TArg): TRes;
    toJsDate: (value: Date) => Date;
    parse: (value: string, formatString: string) => Date | null;
    format: (date: Date, formatKey: keyof DateIOFormats) => string;
    formatByString: (date: Date, formatString: string) => string;
    isEqual: (date: any, comparing: any) => boolean;
    isNull: (date: Date) => date is never;
    isAfterDay: (date: Date, value: Date) => boolean;
    isBeforeDay: (date: Date, value: Date) => boolean;
    isBeforeYear: (date: Date, value: Date) => boolean;
    isBeforeMonth(value: Date, comparing: Date): boolean;
    isAfterMonth(value: Date, comparing: Date): boolean;
    isAfterYear: (date: Date, value: Date) => boolean;
    isWithinRange: (date: Date, [start, end]: [Date, Date]) => boolean;
    formatNumber: (numberToFormat: string) => string;
    getMinutes: (date: Date) => number;
    getDate: (date: Date) => number;
    setDate: (date: Date, count: number) => Date;
    getWeek: (date: Date) => number;
    getMonth: (date: Date) => number;
    getDaysInMonth: (date: Date) => number;
    setMonth: (date: Date, count: number) => Date;
    getMeridiemText: (ampm: "am" | "pm") => "AM" | "PM";
    getNextMonth: (date: Date) => Date;
    getPreviousMonth: (date: Date) => Date;
    getMonthArray: (date: Date) => Date[];
    mergeDateAndTime: (date: Date, time: Date) => Date;
    getWeekdays: () => string[];
    getWeekArray: (date: Date) => Date[][];
    getYearRange: (start: Date, end: Date) => Date[];
}
export {};
