{"ast": null, "code": "export { default } from \"./Table.js\";\nexport { default as tableClasses } from \"./tableClasses.js\";\nexport * from \"./tableClasses.js\";", "map": {"version": 3, "names": ["default", "tableClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Table/index.js"], "sourcesContent": ["export { default } from \"./Table.js\";\nexport { default as tableClasses } from \"./tableClasses.js\";\nexport * from \"./tableClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,YAAY,QAAQ,mBAAmB;AAC3D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}