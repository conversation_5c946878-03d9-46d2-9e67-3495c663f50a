{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\pages\\\\Companies.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Grid, TextField, List, ListItem, ListItemText, ListItemSecondaryAction, Chip, Alert, CircularProgress, InputAdornment } from '@mui/material';\nimport { Search, Business } from '@mui/icons-material';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Companies = () => {\n  _s();\n  const [companies, setCompanies] = useState([]);\n  const [filteredCompanies, setFilteredCompanies] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchQuery, setSearchQuery] = useState('');\n  useEffect(() => {\n    const fetchCompanies = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        const response = await apiService.getTopCompanies({\n          limit: 100\n        });\n        setCompanies(response.data);\n        setFilteredCompanies(response.data);\n      } catch (err) {\n        console.error('Error fetching companies:', err);\n        setError('Failed to load companies data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCompanies();\n  }, []);\n  useEffect(() => {\n    if (!searchQuery) {\n      setFilteredCompanies(companies);\n    } else {\n      const filtered = companies.filter(company => company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) || company.company_name.toLowerCase().includes(searchQuery.toLowerCase()));\n      setFilteredCompanies(filtered);\n    }\n  }, [searchQuery, companies]);\n  const formatCurrency = value => {\n    if (value >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n  const formatDate = dateString => {\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch {\n      return dateString;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Companies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Browse companies with insider trading activity\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Search companies by symbol or name...\",\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"primary.main\",\n                  children: companies.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Companies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"success.main\",\n                  children: companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Transactions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Business, {\n                color: \"info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  color: \"info.main\",\n                  children: formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Total Value\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Companies (\", filteredCompanies.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), filteredCompanies.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          minHeight: \"200px\",\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: searchQuery ? 'No companies found matching your search' : 'No companies data available'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          children: filteredCompanies.map((company, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            divider: index < filteredCompanies.length - 1,\n            sx: {\n              '&:hover': {\n                backgroundColor: 'action.hover'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: 600,\n                  children: company.symbol\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${company.transaction_count} transactions`,\n                  size: \"small\",\n                  variant: \"outlined\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 23\n              }, this),\n              secondary: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.primary\",\n                  children: company.company_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [company.unique_insiders, \" unique insiders \\u2022 Last activity: \", formatDate(company.latest_transaction_date)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"right\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: 600,\n                  color: \"primary.main\",\n                  children: formatCurrency(company.total_value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"success.main\",\n                  children: [\"Buy: \", formatCurrency(company.total_buy_value)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error.main\",\n                  children: [\"Sell: \", formatCurrency(company.total_sell_value)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this)]\n          }, company.symbol, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(Companies, \"DY3Lxjs5YkmYh5Kbt+vTO+5i+y0=\");\n_c = Companies;\nexport default Companies;\nvar _c;\n$RefreshReg$(_c, \"Companies\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "TextField", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Chip", "<PERSON><PERSON>", "CircularProgress", "InputAdornment", "Search", "Business", "apiService", "jsxDEV", "_jsxDEV", "Companies", "_s", "companies", "setCompanies", "filteredCompanies", "setFilteredCompanies", "loading", "setLoading", "error", "setError", "searchQuery", "setSearch<PERSON>uery", "fetchCompanies", "response", "getTopCompanies", "limit", "data", "err", "console", "filtered", "filter", "company", "symbol", "toLowerCase", "includes", "company_name", "formatCurrency", "value", "toFixed", "toLocaleString", "formatDate", "dateString", "Date", "toLocaleDateString", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "severity", "mb", "variant", "gutterBottom", "color", "sx", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "container", "spacing", "item", "xs", "sm", "gap", "length", "reduce", "sum", "c", "transaction_count", "total_value", "map", "index", "divider", "backgroundColor", "primary", "fontWeight", "label", "secondary", "unique_insiders", "latest_transaction_date", "textAlign", "total_buy_value", "total_sell_value", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/pages/Companies.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Grid,\n  TextField,\n  Button,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Chip,\n  Alert,\n  CircularProgress,\n  InputAdornment,\n} from '@mui/material';\nimport { Search, Business } from '@mui/icons-material';\nimport { apiService, CompanyActivity } from '../services/apiService';\n\nconst Companies: React.FC = () => {\n  const [companies, setCompanies] = useState<CompanyActivity[]>([]);\n  const [filteredCompanies, setFilteredCompanies] = useState<CompanyActivity[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  useEffect(() => {\n    const fetchCompanies = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        const response = await apiService.getTopCompanies({ limit: 100 });\n        setCompanies(response.data);\n        setFilteredCompanies(response.data);\n      } catch (err) {\n        console.error('Error fetching companies:', err);\n        setError('Failed to load companies data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCompanies();\n  }, []);\n\n  useEffect(() => {\n    if (!searchQuery) {\n      setFilteredCompanies(companies);\n    } else {\n      const filtered = companies.filter(\n        (company) =>\n          company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||\n          company.company_name.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n      setFilteredCompanies(filtered);\n    }\n  }, [searchQuery, companies]);\n\n  const formatCurrency = (value: number) => {\n    if (value >= 10000000) {\n      return `₹${(value / 10000000).toFixed(1)}Cr`;\n    } else if (value >= 100000) {\n      return `₹${(value / 100000).toFixed(1)}L`;\n    } else {\n      return `₹${value.toLocaleString()}`;\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    try {\n      return new Date(dateString).toLocaleDateString();\n    } catch {\n      return dateString;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert severity=\"error\">{error}</Alert>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" gutterBottom>\n          Companies\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Browse companies with insider trading activity\n        </Typography>\n      </Box>\n\n      {/* Search */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <TextField\n            fullWidth\n            placeholder=\"Search companies by symbol or name...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <Search />\n                </InputAdornment>\n              ),\n            }}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Summary Stats */}\n      <Grid container spacing={3} mb={3}>\n        <Grid item xs={12} sm={4}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"primary\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"primary.main\">\n                    {companies.length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Companies\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={4}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"success\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"success.main\">\n                    {companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Transactions\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={4}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                <Business color=\"info\" />\n                <Box>\n                  <Typography variant=\"h4\" color=\"info.main\">\n                    {formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Total Value\n                  </Typography>\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Companies List */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Companies ({filteredCompanies.length})\n          </Typography>\n          \n          {filteredCompanies.length === 0 ? (\n            <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                {searchQuery ? 'No companies found matching your search' : 'No companies data available'}\n              </Typography>\n            </Box>\n          ) : (\n            <List>\n              {filteredCompanies.map((company, index) => (\n                <ListItem\n                  key={company.symbol}\n                  divider={index < filteredCompanies.length - 1}\n                  sx={{\n                    '&:hover': {\n                      backgroundColor: 'action.hover',\n                    },\n                  }}\n                >\n                  <ListItemText\n                    primary={\n                      <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                        <Typography variant=\"subtitle1\" fontWeight={600}>\n                          {company.symbol}\n                        </Typography>\n                        <Chip\n                          label={`${company.transaction_count} transactions`}\n                          size=\"small\"\n                          variant=\"outlined\"\n                          color=\"primary\"\n                        />\n                      </Box>\n                    }\n                    secondary={\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.primary\">\n                          {company.company_name}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          {company.unique_insiders} unique insiders • \n                          Last activity: {formatDate(company.latest_transaction_date)}\n                        </Typography>\n                      </Box>\n                    }\n                  />\n                  <ListItemSecondaryAction>\n                    <Box textAlign=\"right\">\n                      <Typography variant=\"subtitle1\" fontWeight={600} color=\"primary.main\">\n                        {formatCurrency(company.total_value)}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"success.main\">\n                        Buy: {formatCurrency(company.total_buy_value)}\n                      </Typography>\n                      <br />\n                      <Typography variant=\"caption\" color=\"error.main\">\n                        Sell: {formatCurrency(company.total_sell_value)}\n                      </Typography>\n                    </Box>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default Companies;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,SAAS,EAETC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,QACT,eAAe;AACtB,SAASC,MAAM,EAAEC,QAAQ,QAAQ,qBAAqB;AACtD,SAASC,UAAU,QAAyB,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAoB,EAAE,CAAC;EACjE,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAoB,EAAE,CAAC;EACjF,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMI,QAAQ,GAAG,MAAMhB,UAAU,CAACiB,eAAe,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAC,CAAC;QACjEZ,YAAY,CAACU,QAAQ,CAACG,IAAI,CAAC;QAC3BX,oBAAoB,CAACQ,QAAQ,CAACG,IAAI,CAAC;MACrC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,EAAES,GAAG,CAAC;QAC/CR,QAAQ,CAAC,+BAA+B,CAAC;MAC3C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENhC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,WAAW,EAAE;MAChBL,oBAAoB,CAACH,SAAS,CAAC;IACjC,CAAC,MAAM;MACL,MAAMiB,QAAQ,GAAGjB,SAAS,CAACkB,MAAM,CAC9BC,OAAO,IACNA,OAAO,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CAAC,IAChEF,OAAO,CAACI,YAAY,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACd,WAAW,CAACa,WAAW,CAAC,CAAC,CACzE,CAAC;MACDlB,oBAAoB,CAACc,QAAQ,CAAC;IAChC;EACF,CAAC,EAAE,CAACT,WAAW,EAAER,SAAS,CAAC,CAAC;EAE5B,MAAMwB,cAAc,GAAIC,KAAa,IAAK;IACxC,IAAIA,KAAK,IAAI,QAAQ,EAAE;MACrB,OAAO,IAAI,CAACA,KAAK,GAAG,QAAQ,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;IAC9C,CAAC,MAAM,IAAID,KAAK,IAAI,MAAM,EAAE;MAC1B,OAAO,IAAI,CAACA,KAAK,GAAG,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;IAC3C,CAAC,MAAM;MACL,OAAO,IAAID,KAAK,CAACE,cAAc,CAAC,CAAC,EAAE;IACrC;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,IAAI;MACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;IAClD,CAAC,CAAC,MAAM;MACN,OAAOF,UAAU;IACnB;EACF,CAAC;EAED,IAAIzB,OAAO,EAAE;IACX,oBACEP,OAAA,CAAClB,GAAG;MAACqD,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EvC,OAAA,CAACN,gBAAgB;QAAC8C,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,IAAInC,KAAK,EAAE;IACT,oBACET,OAAA,CAAClB,GAAG;MAAC+D,CAAC,EAAE,CAAE;MAAAN,QAAA,eACRvC,OAAA,CAACP,KAAK;QAACqD,QAAQ,EAAC,OAAO;QAAAP,QAAA,EAAE9B;MAAK;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAEV;EAEA,oBACE5C,OAAA,CAAClB,GAAG;IAAAyD,QAAA,gBAEFvC,OAAA,CAAClB,GAAG;MAACiE,EAAE,EAAE,CAAE;MAAAR,QAAA,gBACTvC,OAAA,CAACjB,UAAU;QAACiE,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAV,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5C,OAAA,CAACjB,UAAU;QAACiE,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAX,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN5C,OAAA,CAAChB,IAAI;MAACmE,EAAE,EAAE;QAAEJ,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBvC,OAAA,CAACf,WAAW;QAAAsD,QAAA,eACVvC,OAAA,CAACb,SAAS;UACRiE,SAAS;UACTC,WAAW,EAAC,uCAAuC;UACnDzB,KAAK,EAAEjB,WAAY;UACnB2C,QAAQ,EAAGC,CAAC,IAAK3C,cAAc,CAAC2C,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;UAChD6B,UAAU,EAAE;YACVC,cAAc,eACZ1D,OAAA,CAACL,cAAc;cAACgE,QAAQ,EAAC,OAAO;cAAApB,QAAA,eAC9BvC,OAAA,CAACJ,MAAM;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP5C,OAAA,CAACd,IAAI;MAAC0E,SAAS;MAACC,OAAO,EAAE,CAAE;MAACd,EAAE,EAAE,CAAE;MAAAR,QAAA,gBAChCvC,OAAA,CAACd,IAAI;QAAC4E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACvBvC,OAAA,CAAChB,IAAI;UAAAuD,QAAA,eACHvC,OAAA,CAACf,WAAW;YAAAsD,QAAA,eACVvC,OAAA,CAAClB,GAAG;cAACqD,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC4B,GAAG,EAAE,CAAE;cAAA1B,QAAA,gBAC7CvC,OAAA,CAACH,QAAQ;gBAACqD,KAAK,EAAC;cAAS;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5B5C,OAAA,CAAClB,GAAG;gBAAAyD,QAAA,gBACFvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC1CpC,SAAS,CAAC+D;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACb5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5C,OAAA,CAACd,IAAI;QAAC4E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACvBvC,OAAA,CAAChB,IAAI;UAAAuD,QAAA,eACHvC,OAAA,CAACf,WAAW;YAAAsD,QAAA,eACVvC,OAAA,CAAClB,GAAG;cAACqD,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC4B,GAAG,EAAE,CAAE;cAAA1B,QAAA,gBAC7CvC,OAAA,CAACH,QAAQ;gBAACqD,KAAK,EAAC;cAAS;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5B5C,OAAA,CAAClB,GAAG;gBAAAyD,QAAA,gBACFvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC1CpC,SAAS,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,iBAAiB,EAAE,CAAC,CAAC,CAACxC,cAAc,CAAC;gBAAC;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC,eACb5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP5C,OAAA,CAACd,IAAI;QAAC4E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACvBvC,OAAA,CAAChB,IAAI;UAAAuD,QAAA,eACHvC,OAAA,CAACf,WAAW;YAAAsD,QAAA,eACVvC,OAAA,CAAClB,GAAG;cAACqD,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAC4B,GAAG,EAAE,CAAE;cAAA1B,QAAA,gBAC7CvC,OAAA,CAACH,QAAQ;gBAACqD,KAAK,EAAC;cAAM;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzB5C,OAAA,CAAClB,GAAG;gBAAAyD,QAAA,gBACFvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,IAAI;kBAACE,KAAK,EAAC,WAAW;kBAAAX,QAAA,EACvCZ,cAAc,CAACxB,SAAS,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACE,WAAW,EAAE,CAAC,CAAC;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACb5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,EAAC;gBAEnD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5C,OAAA,CAAChB,IAAI;MAAAuD,QAAA,eACHvC,OAAA,CAACf,WAAW;QAAAsD,QAAA,gBACVvC,OAAA,CAACjB,UAAU;UAACiE,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAV,QAAA,GAAC,aACzB,EAAClC,iBAAiB,CAAC6D,MAAM,EAAC,GACvC;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZvC,iBAAiB,CAAC6D,MAAM,KAAK,CAAC,gBAC7BlE,OAAA,CAAClB,GAAG;UAACqD,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACC,UAAU,EAAC,QAAQ;UAACC,SAAS,EAAC,OAAO;UAAAC,QAAA,eAC/EvC,OAAA,CAACjB,UAAU;YAACiE,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAX,QAAA,EAC/C5B,WAAW,GAAG,yCAAyC,GAAG;UAA6B;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAEN5C,OAAA,CAACZ,IAAI;UAAAmD,QAAA,EACFlC,iBAAiB,CAACmE,GAAG,CAAC,CAAClD,OAAO,EAAEmD,KAAK,kBACpCzE,OAAA,CAACX,QAAQ;YAEPqF,OAAO,EAAED,KAAK,GAAGpE,iBAAiB,CAAC6D,MAAM,GAAG,CAAE;YAC9Cf,EAAE,EAAE;cACF,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAApC,QAAA,gBAEFvC,OAAA,CAACV,YAAY;cACXsF,OAAO,eACL5E,OAAA,CAAClB,GAAG;gBAACqD,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAAC4B,GAAG,EAAE,CAAE;gBAAA1B,QAAA,gBAC7CvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,WAAW;kBAAC6B,UAAU,EAAE,GAAI;kBAAAtC,QAAA,EAC7CjB,OAAO,CAACC;gBAAM;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACb5C,OAAA,CAACR,IAAI;kBACHsF,KAAK,EAAE,GAAGxD,OAAO,CAACgD,iBAAiB,eAAgB;kBACnD9B,IAAI,EAAC,OAAO;kBACZQ,OAAO,EAAC,UAAU;kBAClBE,KAAK,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;cACDmC,SAAS,eACP/E,OAAA,CAAClB,GAAG;gBAAAyD,QAAA,gBACFvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAC7CjB,OAAO,CAACI;gBAAY;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACb5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,gBAAgB;kBAAAX,QAAA,GACjDjB,OAAO,CAAC0D,eAAe,EAAC,yCACV,EAACjD,UAAU,CAACT,OAAO,CAAC2D,uBAAuB,CAAC;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF5C,OAAA,CAACT,uBAAuB;cAAAgD,QAAA,eACtBvC,OAAA,CAAClB,GAAG;gBAACoG,SAAS,EAAC,OAAO;gBAAA3C,QAAA,gBACpBvC,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,WAAW;kBAAC6B,UAAU,EAAE,GAAI;kBAAC3B,KAAK,EAAC,cAAc;kBAAAX,QAAA,EAClEZ,cAAc,CAACL,OAAO,CAACiD,WAAW;gBAAC;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACb5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,cAAc;kBAAAX,QAAA,GAAC,OAC5C,EAACZ,cAAc,CAACL,OAAO,CAAC6D,eAAe,CAAC;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACb5C,OAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5C,OAAA,CAACjB,UAAU;kBAACiE,OAAO,EAAC,SAAS;kBAACE,KAAK,EAAC,YAAY;kBAAAX,QAAA,GAAC,QACzC,EAACZ,cAAc,CAACL,OAAO,CAAC8D,gBAAgB,CAAC;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACiB,CAAC;UAAA,GA/CrBtB,OAAO,CAACC,MAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAzOID,SAAmB;AAAAoF,EAAA,GAAnBpF,SAAmB;AA2OzB,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}