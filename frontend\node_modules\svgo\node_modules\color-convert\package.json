{"name": "color-convert", "description": "Plain color conversion functions", "version": "1.9.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "Qix-/color-convert", "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "files": ["index.js", "conversions.js", "css-keywords.js", "route.js"], "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "devDependencies": {"chalk": "1.1.1", "xo": "0.11.2"}, "dependencies": {"color-name": "1.1.3"}}