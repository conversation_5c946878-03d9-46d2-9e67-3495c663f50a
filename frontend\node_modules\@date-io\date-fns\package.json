{"name": "@date-io/date-fns", "version": "3.2.1", "description": "Abstraction over common javascript date management libraries", "main": "build/index.js", "module": "build/index.esm.js", "typings": "build/index.d.ts", "scripts": {"build": "rollup -c && tsc -p tsconfig.declaration.json"}, "bugs": {"url": "https://github.com/dmtrKovalenko/date-io/issues"}, "repository": {"type": "git", "url": "https://github.com/dmtrKovalenko/date-io"}, "keywords": ["date", "time", "date-io", "picker", "date-fns", "moment", "luxon"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "dmtr.kova<PERSON><PERSON>@outlook.com"}, "license": "MIT", "peerDependencies": {"date-fns": "^3.2.0 || ^4.1.0"}, "peerDependenciesMeta": {"date-fns": {"optional": true}}, "dependencies": {"@date-io/core": "^3.2.0"}, "devDependencies": {"date-fns": "^4.1.0", "rollup": "^2.0.2", "typescript": "^5.0.0"}, "gitHead": "687eba751bd706e7d704a39a7caa3e0afbfa40aa"}