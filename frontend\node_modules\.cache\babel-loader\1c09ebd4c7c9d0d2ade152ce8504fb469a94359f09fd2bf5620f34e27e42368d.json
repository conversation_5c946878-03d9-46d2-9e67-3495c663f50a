{"ast": null, "code": "export { default } from \"./IconButton.js\";\nexport { default as iconButtonClasses } from \"./iconButtonClasses.js\";\nexport * from \"./iconButtonClasses.js\";", "map": {"version": 3, "names": ["default", "iconButtonClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/IconButton/index.js"], "sourcesContent": ["export { default } from \"./IconButton.js\";\nexport { default as iconButtonClasses } from \"./iconButtonClasses.js\";\nexport * from \"./iconButtonClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,wBAAwB;AACrE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}