{"ast": null, "code": "export { default } from \"./Divider.js\";\nexport { default as dividerClasses } from \"./dividerClasses.js\";\nexport * from \"./dividerClasses.js\";", "map": {"version": 3, "names": ["default", "dividerClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Divider/index.js"], "sourcesContent": ["export { default } from \"./Divider.js\";\nexport { default as dividerClasses } from \"./dividerClasses.js\";\nexport * from \"./dividerClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,cAAc,QAAQ,qBAAqB;AAC/D,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}