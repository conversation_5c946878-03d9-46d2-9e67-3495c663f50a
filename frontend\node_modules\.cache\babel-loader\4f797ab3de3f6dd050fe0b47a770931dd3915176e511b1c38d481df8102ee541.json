{"ast": null, "code": "export default function createMixins(breakpoints, mixins) {\n  return {\n    toolbar: {\n      minHeight: 56,\n      [breakpoints.up('xs')]: {\n        '@media (orientation: landscape)': {\n          minHeight: 48\n        }\n      },\n      [breakpoints.up('sm')]: {\n        minHeight: 64\n      }\n    },\n    ...mixins\n  };\n}", "map": {"version": 3, "names": ["createMixins", "breakpoints", "mixins", "toolbar", "minHeight", "up"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/styles/createMixins.js"], "sourcesContent": ["export default function createMixins(breakpoints, mixins) {\n  return {\n    toolbar: {\n      minHeight: 56,\n      [breakpoints.up('xs')]: {\n        '@media (orientation: landscape)': {\n          minHeight: 48\n        }\n      },\n      [breakpoints.up('sm')]: {\n        minHeight: 64\n      }\n    },\n    ...mixins\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,WAAW,EAAEC,MAAM,EAAE;EACxD,OAAO;IACLC,OAAO,EAAE;MACPC,SAAS,EAAE,EAAE;MACb,CAACH,WAAW,CAACI,EAAE,CAAC,IAAI,CAAC,GAAG;QACtB,iCAAiC,EAAE;UACjCD,SAAS,EAAE;QACb;MACF,CAAC;MACD,CAACH,WAAW,CAACI,EAAE,CAAC,IAAI,CAAC,GAAG;QACtBD,SAAS,EAAE;MACb;IACF,CAAC;IACD,GAAGF;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}