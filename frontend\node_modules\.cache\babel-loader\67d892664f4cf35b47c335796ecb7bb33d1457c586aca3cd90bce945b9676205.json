{"ast": null, "code": "export { default } from \"./TablePagination.js\";\nexport { default as tablePaginationClasses } from \"./tablePaginationClasses.js\";\nexport * from \"./tablePaginationClasses.js\";", "map": {"version": 3, "names": ["default", "tablePaginationClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/TablePagination/index.js"], "sourcesContent": ["export { default } from \"./TablePagination.js\";\nexport { default as tablePaginationClasses } from \"./tablePaginationClasses.js\";\nexport * from \"./tablePaginationClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB;AAC9C,SAASA,OAAO,IAAIC,sBAAsB,QAAQ,6BAA6B;AAC/E,cAAc,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}