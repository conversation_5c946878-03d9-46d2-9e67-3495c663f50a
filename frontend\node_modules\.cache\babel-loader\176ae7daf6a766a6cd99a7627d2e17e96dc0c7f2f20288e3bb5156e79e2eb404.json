{"ast": null, "code": "var _jsxFileName = \"D:\\\\chirag\\\\nsescrapper\\\\frontend\\\\src\\\\components\\\\StatCard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Card, CardContent, Typography, Box, useTheme } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatCard = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color = 'primary',\n  trend\n}) => {\n  _s();\n  const theme = useTheme();\n  const getColorValue = colorName => {\n    switch (colorName) {\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n  const colorValue = getColorValue(color);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    sx: {\n      height: '100%',\n      position: 'relative',\n      overflow: 'visible',\n      '&:hover': {\n        transform: 'translateY(-2px)',\n        boxShadow: theme.shadows[8]\n      },\n      transition: 'all 0.3s ease-in-out'\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        pb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'flex-start',\n          justifyContent: 'space-between',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flex: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            sx: {\n              fontSize: '0.875rem',\n              fontWeight: 500\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"div\",\n            sx: {\n              fontWeight: 700,\n              color: colorValue,\n              fontSize: {\n                xs: '1.5rem',\n                sm: '2rem'\n              },\n              lineHeight: 1.2,\n              mb: subtitle ? 0.5 : 0\n            },\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), subtitle && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontSize: '0.75rem'\n            },\n            children: subtitle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            width: 48,\n            height: 48,\n            borderRadius: '12px',\n            backgroundColor: colorValue + '20',\n            color: colorValue,\n            ml: 2\n          },\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), trend && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: trend.isPositive ? theme.palette.success.main : theme.palette.error.main,\n            fontWeight: 600,\n            fontSize: '0.75rem'\n          },\n          children: [trend.isPositive ? '+' : '', trend.value, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            fontSize: '0.75rem'\n          },\n          children: \"vs last period\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(StatCard, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = StatCard;\nexport default StatCard;\nvar _c;\n$RefreshReg$(_c, \"StatCard\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "useTheme", "jsxDEV", "_jsxDEV", "StatCard", "title", "value", "subtitle", "icon", "color", "trend", "_s", "theme", "getColorValue", "colorName", "palette", "primary", "main", "secondary", "success", "error", "warning", "info", "colorValue", "sx", "height", "position", "overflow", "transform", "boxShadow", "shadows", "transition", "children", "pb", "display", "alignItems", "justifyContent", "mb", "flex", "variant", "gutterBottom", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "xs", "sm", "lineHeight", "width", "borderRadius", "backgroundColor", "ml", "gap", "isPositive", "_c", "$RefreshReg$"], "sources": ["D:/chirag/nsescrapper/frontend/src/components/StatCard.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  useTheme,\n} from '@mui/material';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon: React.ReactNode;\n  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n}\n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color = 'primary',\n  trend,\n}) => {\n  const theme = useTheme();\n\n  const getColorValue = (colorName: string) => {\n    switch (colorName) {\n      case 'primary':\n        return theme.palette.primary.main;\n      case 'secondary':\n        return theme.palette.secondary.main;\n      case 'success':\n        return theme.palette.success.main;\n      case 'error':\n        return theme.palette.error.main;\n      case 'warning':\n        return theme.palette.warning.main;\n      case 'info':\n        return theme.palette.info.main;\n      default:\n        return theme.palette.primary.main;\n    }\n  };\n\n  const colorValue = getColorValue(color);\n\n  return (\n    <Card\n      sx={{\n        height: '100%',\n        position: 'relative',\n        overflow: 'visible',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: theme.shadows[8],\n        },\n        transition: 'all 0.3s ease-in-out',\n      }}\n    >\n      <CardContent sx={{ pb: 2 }}>\n        <Box\n          sx={{\n            display: 'flex',\n            alignItems: 'flex-start',\n            justifyContent: 'space-between',\n            mb: 2,\n          }}\n        >\n          <Box sx={{ flex: 1 }}>\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n              gutterBottom\n              sx={{ fontSize: '0.875rem', fontWeight: 500 }}\n            >\n              {title}\n            </Typography>\n            <Typography\n              variant=\"h4\"\n              component=\"div\"\n              sx={{\n                fontWeight: 700,\n                color: colorValue,\n                fontSize: { xs: '1.5rem', sm: '2rem' },\n                lineHeight: 1.2,\n                mb: subtitle ? 0.5 : 0,\n              }}\n            >\n              {value}\n            </Typography>\n            {subtitle && (\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                sx={{ fontSize: '0.75rem' }}\n              >\n                {subtitle}\n              </Typography>\n            )}\n          </Box>\n          <Box\n            sx={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: 48,\n              height: 48,\n              borderRadius: '12px',\n              backgroundColor: colorValue + '20',\n              color: colorValue,\n              ml: 2,\n            }}\n          >\n            {icon}\n          </Box>\n        </Box>\n\n        {trend && (\n          <Box\n            sx={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: 0.5,\n            }}\n          >\n            <Typography\n              variant=\"body2\"\n              sx={{\n                color: trend.isPositive ? theme.palette.success.main : theme.palette.error.main,\n                fontWeight: 600,\n                fontSize: '0.75rem',\n              }}\n            >\n              {trend.isPositive ? '+' : ''}{trend.value}%\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n              sx={{ fontSize: '0.75rem' }}\n            >\n              vs last period\n            </Typography>\n          </Box>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default StatCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,QAAQ,QACH,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcvB,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,IAAI;EACJC,KAAK,GAAG,SAAS;EACjBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGX,QAAQ,CAAC,CAAC;EAExB,MAAMY,aAAa,GAAIC,SAAiB,IAAK;IAC3C,QAAQA,SAAS;MACf,KAAK,SAAS;QACZ,OAAOF,KAAK,CAACG,OAAO,CAACC,OAAO,CAACC,IAAI;MACnC,KAAK,WAAW;QACd,OAAOL,KAAK,CAACG,OAAO,CAACG,SAAS,CAACD,IAAI;MACrC,KAAK,SAAS;QACZ,OAAOL,KAAK,CAACG,OAAO,CAACI,OAAO,CAACF,IAAI;MACnC,KAAK,OAAO;QACV,OAAOL,KAAK,CAACG,OAAO,CAACK,KAAK,CAACH,IAAI;MACjC,KAAK,SAAS;QACZ,OAAOL,KAAK,CAACG,OAAO,CAACM,OAAO,CAACJ,IAAI;MACnC,KAAK,MAAM;QACT,OAAOL,KAAK,CAACG,OAAO,CAACO,IAAI,CAACL,IAAI;MAChC;QACE,OAAOL,KAAK,CAACG,OAAO,CAACC,OAAO,CAACC,IAAI;IACrC;EACF,CAAC;EAED,MAAMM,UAAU,GAAGV,aAAa,CAACJ,KAAK,CAAC;EAEvC,oBACEN,OAAA,CAACN,IAAI;IACH2B,EAAE,EAAE;MACFC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE;QACTC,SAAS,EAAE,kBAAkB;QAC7BC,SAAS,EAAEjB,KAAK,CAACkB,OAAO,CAAC,CAAC;MAC5B,CAAC;MACDC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eAEF7B,OAAA,CAACL,WAAW;MAAC0B,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACzB7B,OAAA,CAACH,GAAG;QACFwB,EAAE,EAAE;UACFU,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,YAAY;UACxBC,cAAc,EAAE,eAAe;UAC/BC,EAAE,EAAE;QACN,CAAE;QAAAL,QAAA,gBAEF7B,OAAA,CAACH,GAAG;UAACwB,EAAE,EAAE;YAAEc,IAAI,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACnB7B,OAAA,CAACJ,UAAU;YACTwC,OAAO,EAAC,OAAO;YACf9B,KAAK,EAAC,gBAAgB;YACtB+B,YAAY;YACZhB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,UAAU;cAAEC,UAAU,EAAE;YAAI,CAAE;YAAAV,QAAA,EAE7C3B;UAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACb3C,OAAA,CAACJ,UAAU;YACTwC,OAAO,EAAC,IAAI;YACZQ,SAAS,EAAC,KAAK;YACfvB,EAAE,EAAE;cACFkB,UAAU,EAAE,GAAG;cACfjC,KAAK,EAAEc,UAAU;cACjBkB,QAAQ,EAAE;gBAAEO,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACtCC,UAAU,EAAE,GAAG;cACfb,EAAE,EAAE9B,QAAQ,GAAG,GAAG,GAAG;YACvB,CAAE;YAAAyB,QAAA,EAED1B;UAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACZvC,QAAQ,iBACPJ,OAAA,CAACJ,UAAU;YACTwC,OAAO,EAAC,OAAO;YACf9B,KAAK,EAAC,gBAAgB;YACtBe,EAAE,EAAE;cAAEiB,QAAQ,EAAE;YAAU,CAAE;YAAAT,QAAA,EAE3BzB;UAAQ;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3C,OAAA,CAACH,GAAG;UACFwB,EAAE,EAAE;YACFU,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBe,KAAK,EAAE,EAAE;YACT1B,MAAM,EAAE,EAAE;YACV2B,YAAY,EAAE,MAAM;YACpBC,eAAe,EAAE9B,UAAU,GAAG,IAAI;YAClCd,KAAK,EAAEc,UAAU;YACjB+B,EAAE,EAAE;UACN,CAAE;UAAAtB,QAAA,EAEDxB;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELpC,KAAK,iBACJP,OAAA,CAACH,GAAG;QACFwB,EAAE,EAAE;UACFU,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBoB,GAAG,EAAE;QACP,CAAE;QAAAvB,QAAA,gBAEF7B,OAAA,CAACJ,UAAU;UACTwC,OAAO,EAAC,OAAO;UACff,EAAE,EAAE;YACFf,KAAK,EAAEC,KAAK,CAAC8C,UAAU,GAAG5C,KAAK,CAACG,OAAO,CAACI,OAAO,CAACF,IAAI,GAAGL,KAAK,CAACG,OAAO,CAACK,KAAK,CAACH,IAAI;YAC/EyB,UAAU,EAAE,GAAG;YACfD,QAAQ,EAAE;UACZ,CAAE;UAAAT,QAAA,GAEDtB,KAAK,CAAC8C,UAAU,GAAG,GAAG,GAAG,EAAE,EAAE9C,KAAK,CAACJ,KAAK,EAAC,GAC5C;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAACJ,UAAU;UACTwC,OAAO,EAAC,OAAO;UACf9B,KAAK,EAAC,gBAAgB;UACtBe,EAAE,EAAE;YAAEiB,QAAQ,EAAE;UAAU,CAAE;UAAAT,QAAA,EAC7B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACnC,EAAA,CApIIP,QAAiC;EAAA,QAQvBH,QAAQ;AAAA;AAAAwD,EAAA,GARlBrD,QAAiC;AAsIvC,eAAeA,QAAQ;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}