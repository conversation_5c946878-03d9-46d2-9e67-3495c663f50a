{"ast": null, "code": "export { default } from \"./Popover.js\";\nexport * from \"./Popover.js\";\nexport { default as popoverClasses } from \"./popoverClasses.js\";\nexport * from \"./popoverClasses.js\";", "map": {"version": 3, "names": ["default", "popoverClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Popover/index.js"], "sourcesContent": ["export { default } from \"./Popover.js\";\nexport * from \"./Popover.js\";\nexport { default as popoverClasses } from \"./popoverClasses.js\";\nexport * from \"./popoverClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc;AAC5B,SAASA,OAAO,IAAIC,cAAc,QAAQ,qBAAqB;AAC/D,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}