{"ast": null, "code": "// track, thumb and active are derived from macOS 10.15.7\nconst scrollBar = {\n  track: '#2b2b2b',\n  thumb: '#6b6b6b',\n  active: '#959595'\n};\nexport default function darkScrollbar(options = scrollBar) {\n  return {\n    scrollbarColor: `${options.thumb} ${options.track}`,\n    '&::-webkit-scrollbar, & *::-webkit-scrollbar': {\n      backgroundColor: options.track\n    },\n    '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {\n      borderRadius: 8,\n      backgroundColor: options.thumb,\n      minHeight: 24,\n      border: `3px solid ${options.track}`\n    },\n    '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-corner, & *::-webkit-scrollbar-corner': {\n      backgroundColor: options.track\n    }\n  };\n}", "map": {"version": 3, "names": ["scrollBar", "track", "thumb", "active", "darkScrollbar", "options", "scrollbarColor", "backgroundColor", "borderRadius", "minHeight", "border"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/darkScrollbar/index.js"], "sourcesContent": ["// track, thumb and active are derived from macOS 10.15.7\nconst scrollBar = {\n  track: '#2b2b2b',\n  thumb: '#6b6b6b',\n  active: '#959595'\n};\nexport default function darkScrollbar(options = scrollBar) {\n  return {\n    scrollbarColor: `${options.thumb} ${options.track}`,\n    '&::-webkit-scrollbar, & *::-webkit-scrollbar': {\n      backgroundColor: options.track\n    },\n    '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {\n      borderRadius: 8,\n      backgroundColor: options.thumb,\n      minHeight: 24,\n      border: `3px solid ${options.track}`\n    },\n    '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-corner, & *::-webkit-scrollbar-corner': {\n      backgroundColor: options.track\n    }\n  };\n}"], "mappings": "AAAA;AACA,MAAMA,SAAS,GAAG;EAChBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE;AACV,CAAC;AACD,eAAe,SAASC,aAAaA,CAACC,OAAO,GAAGL,SAAS,EAAE;EACzD,OAAO;IACLM,cAAc,EAAE,GAAGD,OAAO,CAACH,KAAK,IAAIG,OAAO,CAACJ,KAAK,EAAE;IACnD,8CAA8C,EAAE;MAC9CM,eAAe,EAAEF,OAAO,CAACJ;IAC3B,CAAC;IACD,0DAA0D,EAAE;MAC1DO,YAAY,EAAE,CAAC;MACfD,eAAe,EAAEF,OAAO,CAACH,KAAK;MAC9BO,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,aAAaL,OAAO,CAACJ,KAAK;IACpC,CAAC;IACD,sEAAsE,EAAE;MACtEM,eAAe,EAAEF,OAAO,CAACF;IAC3B,CAAC;IACD,wEAAwE,EAAE;MACxEI,eAAe,EAAEF,OAAO,CAACF;IAC3B,CAAC;IACD,sEAAsE,EAAE;MACtEI,eAAe,EAAEF,OAAO,CAACF;IAC3B,CAAC;IACD,4DAA4D,EAAE;MAC5DI,eAAe,EAAEF,OAAO,CAACJ;IAC3B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}