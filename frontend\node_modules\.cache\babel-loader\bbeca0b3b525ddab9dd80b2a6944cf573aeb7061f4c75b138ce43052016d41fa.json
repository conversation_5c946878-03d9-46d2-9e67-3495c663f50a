{"ast": null, "code": "export { default } from \"./Icon.js\";\nexport { default as iconClasses } from \"./iconClasses.js\";\nexport * from \"./iconClasses.js\";", "map": {"version": 3, "names": ["default", "iconClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Icon/index.js"], "sourcesContent": ["export { default } from \"./Icon.js\";\nexport { default as iconClasses } from \"./iconClasses.js\";\nexport * from \"./iconClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASA,OAAO,IAAIC,WAAW,QAAQ,kBAAkB;AACzD,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}