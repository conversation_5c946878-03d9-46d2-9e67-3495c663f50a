{"ast": null, "code": "export { default } from \"./DialogTitle.js\";\nexport { default as dialogTitleClasses } from \"./dialogTitleClasses.js\";\nexport * from \"./dialogTitleClasses.js\";", "map": {"version": 3, "names": ["default", "dialogTitleClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/DialogTitle/index.js"], "sourcesContent": ["export { default } from \"./DialogTitle.js\";\nexport { default as dialogTitleClasses } from \"./dialogTitleClasses.js\";\nexport * from \"./dialogTitleClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}