{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getYear} function options.\n */\n\n/**\n * @name getYear\n * @category Year Helpers\n * @summary Get the year of the given date.\n *\n * @description\n * Get the year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year\n *\n * @example\n * // Which year is 2 July 2014?\n * const result = getYear(new Date(2014, 6, 2))\n * //=> 2014\n */\nexport function getYear(date, options) {\n  return toDate(date, options?.in).getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default getYear;", "map": {"version": 3, "names": ["toDate", "getYear", "date", "options", "in", "getFullYear"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/date-fns/getYear.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getYear} function options.\n */\n\n/**\n * @name getYear\n * @category Year Helpers\n * @summary Get the year of the given date.\n *\n * @description\n * Get the year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year\n *\n * @example\n * // Which year is 2 July 2014?\n * const result = getYear(new Date(2014, 6, 2))\n * //=> 2014\n */\nexport function getYear(date, options) {\n  return toDate(date, options?.in).getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default getYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACrC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;AAChD;;AAEA;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}