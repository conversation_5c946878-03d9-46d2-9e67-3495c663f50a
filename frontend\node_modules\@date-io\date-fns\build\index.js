'use strict';

var addDays = require('date-fns/addDays');
var addSeconds = require('date-fns/addSeconds');
var addMinutes = require('date-fns/addMinutes');
var addHours = require('date-fns/addHours');
var addWeeks = require('date-fns/addWeeks');
var addMonths = require('date-fns/addMonths');
var addYears = require('date-fns/addYears');
var differenceInYears = require('date-fns/differenceInYears');
var differenceInQuarters = require('date-fns/differenceInQuarters');
var differenceInMonths = require('date-fns/differenceInMonths');
var differenceInWeeks = require('date-fns/differenceInWeeks');
var differenceInDays = require('date-fns/differenceInDays');
var differenceInHours = require('date-fns/differenceInHours');
var differenceInMinutes = require('date-fns/differenceInMinutes');
var differenceInSeconds = require('date-fns/differenceInSeconds');
var differenceInMilliseconds = require('date-fns/differenceInMilliseconds');
var eachDayOfInterval = require('date-fns/eachDayOfInterval');
var endOfDay = require('date-fns/endOfDay');
var endOfWeek = require('date-fns/endOfWeek');
var endOfYear = require('date-fns/endOfYear');
var format = require('date-fns/format');
var getDate = require('date-fns/getDate');
var getDay = require('date-fns/getDay');
var getDaysInMonth = require('date-fns/getDaysInMonth');
var getHours = require('date-fns/getHours');
var getMinutes = require('date-fns/getMinutes');
var getWeek = require('date-fns/getWeek');
var getMonth = require('date-fns/getMonth');
var getSeconds = require('date-fns/getSeconds');
var getYear = require('date-fns/getYear');
var isAfter = require('date-fns/isAfter');
var isBefore = require('date-fns/isBefore');
var isEqual = require('date-fns/isEqual');
var isSameDay = require('date-fns/isSameDay');
var isSameYear = require('date-fns/isSameYear');
var isSameMonth = require('date-fns/isSameMonth');
var isSameHour = require('date-fns/isSameHour');
var isValid = require('date-fns/isValid');
var parse = require('date-fns/parse');
var setDate = require('date-fns/setDate');
var setHours = require('date-fns/setHours');
var setMinutes = require('date-fns/setMinutes');
var setMonth = require('date-fns/setMonth');
var setSeconds = require('date-fns/setSeconds');
var setYear = require('date-fns/setYear');
var startOfDay = require('date-fns/startOfDay');
var startOfMonth = require('date-fns/startOfMonth');
var endOfMonth = require('date-fns/endOfMonth');
var startOfWeek = require('date-fns/startOfWeek');
var startOfYear = require('date-fns/startOfYear');
var parseISO = require('date-fns/parseISO');
var formatISO = require('date-fns/formatISO');
var isWithinInterval = require('date-fns/isWithinInterval');
var enUS = require('date-fns/locale/en-US');

const defaultFormats = {
    dayOfMonth: "d",
    fullDate: "PP",
    fullDateWithWeekday: "PPPP",
    fullDateTime: "PP p",
    fullDateTime12h: "PP hh:mm aa",
    fullDateTime24h: "PP HH:mm",
    fullTime: "p",
    fullTime12h: "hh:mm aa",
    fullTime24h: "HH:mm",
    hours12h: "hh",
    hours24h: "HH",
    keyboardDate: "P",
    keyboardDateTime: "P p",
    keyboardDateTime12h: "P hh:mm aa",
    keyboardDateTime24h: "P HH:mm",
    minutes: "mm",
    month: "LLLL",
    monthAndDate: "MMMM d",
    monthAndYear: "LLLL yyyy",
    monthShort: "MMM",
    weekday: "EEEE",
    weekdayShort: "EEE",
    normalDate: "d MMMM",
    normalDateWithWeekday: "EEE, MMM d",
    seconds: "ss",
    shortDate: "MMM d",
    year: "yyyy",
};
class DateFnsUtils {
    constructor({ locale, formats, } = {}) {
        this.lib = "date-fns";
        // Note: date-fns input types are more lenient than this adapter, so we need to expose our more
        // strict signature and delegate to the more lenient signature. Otherwise, we have downstream type errors upon usage.
        this.is12HourCycleInCurrentLocale = () => {
            var _a;
            if (this.locale) {
                return /a/.test((_a = this.locale.formatLong) === null || _a === void 0 ? void 0 : _a.time({}));
            }
            // By default date-fns is using en-US locale with am/pm enabled
            return true;
        };
        this.getFormatHelperText = (format$1) => {
            var _a, _b;
            // @see https://github.com/date-fns/date-fns/blob/master/src/format/index.js#L31
            const longFormatRegexp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
            const locale = this.locale || enUS.enUS;
            return ((_b = (_a = format$1
                .match(longFormatRegexp)) === null || _a === void 0 ? void 0 : _a.map((token) => {
                const firstCharacter = token[0];
                if (firstCharacter === "p" || firstCharacter === "P") {
                    const longFormatter = format.longFormatters[firstCharacter];
                    return longFormatter(token, locale.formatLong);
                }
                return token;
            }).join("").replace(/(aaa|aa|a)/g, "(a|p)m").toLocaleLowerCase()) !== null && _b !== void 0 ? _b : format$1);
        };
        this.parseISO = (isoString) => {
            return parseISO.parseISO(isoString);
        };
        this.toISO = (value) => {
            return formatISO.formatISO(value, { format: "extended" });
        };
        this.getCurrentLocaleCode = () => {
            var _a;
            return ((_a = this.locale) === null || _a === void 0 ? void 0 : _a.code) || "en-US";
        };
        this.addSeconds = (value, count) => {
            return addSeconds.addSeconds(value, count);
        };
        this.addMinutes = (value, count) => {
            return addMinutes.addMinutes(value, count);
        };
        this.addHours = (value, count) => {
            return addHours.addHours(value, count);
        };
        this.addDays = (value, count) => {
            return addDays.addDays(value, count);
        };
        this.addWeeks = (value, count) => {
            return addWeeks.addWeeks(value, count);
        };
        this.addMonths = (value, count) => {
            return addMonths.addMonths(value, count);
        };
        this.addYears = (value, count) => {
            return addYears.addYears(value, count);
        };
        this.isValid = (value) => {
            return isValid.isValid(this.date(value));
        };
        this.getDiff = (value, comparing, unit) => {
            var _a;
            // we output 0 if the compare date is string and parsing is not valid
            const dateToCompare = (_a = this.date(comparing)) !== null && _a !== void 0 ? _a : value;
            if (!this.isValid(dateToCompare)) {
                return 0;
            }
            switch (unit) {
                case "years":
                    return differenceInYears.differenceInYears(value, dateToCompare);
                case "quarters":
                    return differenceInQuarters.differenceInQuarters(value, dateToCompare);
                case "months":
                    return differenceInMonths.differenceInMonths(value, dateToCompare);
                case "weeks":
                    return differenceInWeeks.differenceInWeeks(value, dateToCompare);
                case "days":
                    return differenceInDays.differenceInDays(value, dateToCompare);
                case "hours":
                    return differenceInHours.differenceInHours(value, dateToCompare);
                case "minutes":
                    return differenceInMinutes.differenceInMinutes(value, dateToCompare);
                case "seconds":
                    return differenceInSeconds.differenceInSeconds(value, dateToCompare);
                default: {
                    return differenceInMilliseconds.differenceInMilliseconds(value, dateToCompare);
                }
            }
        };
        this.isAfter = (value, comparing) => {
            return isAfter.isAfter(value, comparing);
        };
        this.isBefore = (value, comparing) => {
            return isBefore.isBefore(value, comparing);
        };
        this.startOfDay = (value) => {
            return startOfDay.startOfDay(value);
        };
        this.endOfDay = (value) => {
            return endOfDay.endOfDay(value);
        };
        this.getHours = (value) => {
            return getHours.getHours(value);
        };
        this.setHours = (value, count) => {
            return setHours.setHours(value, count);
        };
        this.setMinutes = (value, count) => {
            return setMinutes.setMinutes(value, count);
        };
        this.getSeconds = (value) => {
            return getSeconds.getSeconds(value);
        };
        this.setSeconds = (value, count) => {
            return setSeconds.setSeconds(value, count);
        };
        this.isSameDay = (value, comparing) => {
            return isSameDay.isSameDay(value, comparing);
        };
        this.isSameMonth = (value, comparing) => {
            return isSameMonth.isSameMonth(value, comparing);
        };
        this.isSameYear = (value, comparing) => {
            return isSameYear.isSameYear(value, comparing);
        };
        this.isSameHour = (value, comparing) => {
            return isSameHour.isSameHour(value, comparing);
        };
        this.startOfYear = (value) => {
            return startOfYear.startOfYear(value);
        };
        this.endOfYear = (value) => {
            return endOfYear.endOfYear(value);
        };
        this.startOfMonth = (value) => {
            return startOfMonth.startOfMonth(value);
        };
        this.endOfMonth = (value) => {
            return endOfMonth.endOfMonth(value);
        };
        this.startOfWeek = (value) => {
            return startOfWeek.startOfWeek(value, { locale: this.locale });
        };
        this.endOfWeek = (value) => {
            return endOfWeek.endOfWeek(value, { locale: this.locale });
        };
        this.getYear = (value) => {
            return getYear.getYear(value);
        };
        this.setYear = (value, count) => {
            return setYear.setYear(value, count);
        };
        this.toJsDate = (value) => {
            return value;
        };
        this.parse = (value, formatString) => {
            if (value === "") {
                return null;
            }
            return parse.parse(value, formatString, new Date(), { locale: this.locale });
        };
        this.format = (date, formatKey) => {
            return this.formatByString(date, this.formats[formatKey]);
        };
        this.formatByString = (date, formatString) => {
            return format.format(date, formatString, { locale: this.locale });
        };
        this.isEqual = (date, comparing) => {
            if (date === null && comparing === null) {
                return true;
            }
            return isEqual.isEqual(date, comparing);
        };
        this.isNull = (date) => {
            return date === null;
        };
        this.isAfterDay = (date, value) => {
            return isAfter.isAfter(date, endOfDay.endOfDay(value));
        };
        this.isBeforeDay = (date, value) => {
            return isBefore.isBefore(date, startOfDay.startOfDay(value));
        };
        this.isBeforeYear = (date, value) => {
            return isBefore.isBefore(date, startOfYear.startOfYear(value));
        };
        this.isAfterYear = (date, value) => {
            return isAfter.isAfter(date, endOfYear.endOfYear(value));
        };
        this.isWithinRange = (date, [start, end]) => {
            return isWithinInterval.isWithinInterval(date, { start, end });
        };
        this.formatNumber = (numberToFormat) => {
            return numberToFormat;
        };
        this.getMinutes = (date) => {
            return getMinutes.getMinutes(date);
        };
        this.getDate = (date) => {
            return getDate.getDate(date);
        };
        this.setDate = (date, count) => {
            return setDate.setDate(date, count);
        };
        this.getWeek = (date) => {
            return getWeek.getWeek(date);
        };
        this.getMonth = (date) => {
            return getMonth.getMonth(date);
        };
        this.getDaysInMonth = (date) => {
            return getDaysInMonth.getDaysInMonth(date);
        };
        this.setMonth = (date, count) => {
            return setMonth.setMonth(date, count);
        };
        this.getMeridiemText = (ampm) => {
            return ampm === "am" ? "AM" : "PM";
        };
        this.getNextMonth = (date) => {
            return addMonths.addMonths(date, 1);
        };
        this.getPreviousMonth = (date) => {
            return addMonths.addMonths(date, -1);
        };
        this.getMonthArray = (date) => {
            const firstMonth = startOfYear.startOfYear(date);
            const monthArray = [firstMonth];
            while (monthArray.length < 12) {
                const prevMonth = monthArray[monthArray.length - 1];
                monthArray.push(this.getNextMonth(prevMonth));
            }
            return monthArray;
        };
        this.mergeDateAndTime = (date, time) => {
            return this.setSeconds(this.setMinutes(this.setHours(date, this.getHours(time)), this.getMinutes(time)), this.getSeconds(time));
        };
        this.getWeekdays = () => {
            const now = new Date();
            return eachDayOfInterval.eachDayOfInterval({
                start: startOfWeek.startOfWeek(now, { locale: this.locale }),
                end: endOfWeek.endOfWeek(now, { locale: this.locale }),
            }).map((day) => this.formatByString(day, "EEEEEE"));
        };
        this.getWeekArray = (date) => {
            const start = startOfWeek.startOfWeek(startOfMonth.startOfMonth(date), { locale: this.locale });
            const end = endOfWeek.endOfWeek(endOfMonth.endOfMonth(date), { locale: this.locale });
            let count = 0;
            let current = start;
            const nestedWeeks = [];
            let lastDay = null;
            while (isBefore.isBefore(current, end)) {
                const weekNumber = Math.floor(count / 7);
                nestedWeeks[weekNumber] = nestedWeeks[weekNumber] || [];
                const day = getDay.getDay(current);
                if (lastDay !== day) {
                    lastDay = day;
                    nestedWeeks[weekNumber].push(current);
                    count += 1;
                }
                current = addDays.addDays(current, 1);
            }
            return nestedWeeks;
        };
        this.getYearRange = (start, end) => {
            const startDate = startOfYear.startOfYear(start);
            const endDate = endOfYear.endOfYear(end);
            const years = [];
            let current = startDate;
            while (isBefore.isBefore(current, endDate)) {
                years.push(current);
                current = addYears.addYears(current, 1);
            }
            return years;
        };
        this.locale = locale;
        this.formats = Object.assign({}, defaultFormats, formats);
    }
    date(value) {
        if (typeof value === "undefined") {
            return new Date();
        }
        if (value === null) {
            return null;
        }
        return new Date(value);
    }
    isBeforeMonth(value, comparing) {
        return isBefore.isBefore(value, startOfMonth.startOfMonth(comparing));
    }
    isAfterMonth(value, comparing) {
        return isAfter.isAfter(value, startOfMonth.startOfMonth(comparing));
    }
}

module.exports = DateFnsUtils;
