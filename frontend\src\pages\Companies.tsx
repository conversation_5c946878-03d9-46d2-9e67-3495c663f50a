import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import { Search, Business } from '@mui/icons-material';
import { apiService, CompanyActivity } from '../services/apiService';

const Companies: React.FC = () => {
  const [companies, setCompanies] = useState<CompanyActivity[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<CompanyActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await apiService.getTopCompanies({ limit: 100 });
        setCompanies(response.data);
        setFilteredCompanies(response.data);
      } catch (err) {
        console.error('Error fetching companies:', err);
        setError('Failed to load companies data');
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  useEffect(() => {
    if (!searchQuery) {
      setFilteredCompanies(companies);
    } else {
      const filtered = companies.filter(
        (company) =>
          company.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
          company.company_name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCompanies(filtered);
    }
  }, [searchQuery, companies]);

  const formatCurrency = (value: number) => {
    if (value >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (value >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          Companies
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Browse companies with insider trading activity
        </Typography>
      </Box>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search companies by symbol or name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Business color="primary" />
                <Box>
                  <Typography variant="h4" color="primary.main">
                    {companies.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Companies
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Business color="success" />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {companies.reduce((sum, c) => sum + c.transaction_count, 0).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Transactions
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Business color="info" />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {formatCurrency(companies.reduce((sum, c) => sum + c.total_value, 0))}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Value
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Companies List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Companies ({filteredCompanies.length})
          </Typography>
          
          {filteredCompanies.length === 0 ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <Typography variant="body1" color="text.secondary">
                {searchQuery ? 'No companies found matching your search' : 'No companies data available'}
              </Typography>
            </Box>
          ) : (
            <List>
              {filteredCompanies.map((company, index) => (
                <ListItem
                  key={company.symbol}
                  divider={index < filteredCompanies.length - 1}
                  sx={{
                    '&:hover': {
                      backgroundColor: 'action.hover',
                    },
                  }}
                >
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="subtitle1" fontWeight={600}>
                          {company.symbol}
                        </Typography>
                        <Chip
                          label={`${company.transaction_count} transactions`}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.primary">
                          {company.company_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {company.unique_insiders} unique insiders • 
                          Last activity: {formatDate(company.latest_transaction_date)}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box textAlign="right">
                      <Typography variant="subtitle1" fontWeight={600} color="primary.main">
                        {formatCurrency(company.total_value)}
                      </Typography>
                      <Typography variant="caption" color="success.main">
                        Buy: {formatCurrency(company.total_buy_value)}
                      </Typography>
                      <br />
                      <Typography variant="caption" color="error.main">
                        Sell: {formatCurrency(company.total_sell_value)}
                      </Typography>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Companies;
