{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameMonth} function options.\n */\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return laterDate_.getFullYear() === earlierDate_.getFullYear() && laterDate_.getMonth() === earlierDate_.getMonth();\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;", "map": {"version": 3, "names": ["normalizeDates", "isSameMonth", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "getFullYear", "getMonth"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/date-fns/isSameMonth.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameMonth} function options.\n */\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    laterDate_.getFullYear() === earlierDate_.getFullYear() &&\n    laterDate_.getMonth() === earlierDate_.getMonth()\n  );\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC3D,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGN,cAAc,CAC/CI,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OACEE,UAAU,CAACG,WAAW,CAAC,CAAC,KAAKF,YAAY,CAACE,WAAW,CAAC,CAAC,IACvDH,UAAU,CAACI,QAAQ,CAAC,CAAC,KAAKH,YAAY,CAACG,QAAQ,CAAC,CAAC;AAErD;;AAEA;AACA,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}