{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport function getMilliseconds(date) {\n  return toDate(date).getMilliseconds();\n}\n\n// Fallback for modularized imports:\nexport default getMilliseconds;", "map": {"version": 3, "names": ["toDate", "getMilliseconds", "date"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/date-fns/getMilliseconds.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport function getMilliseconds(date) {\n  return toDate(date).getMilliseconds();\n}\n\n// Fallback for modularized imports:\nexport default getMilliseconds;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAOF,MAAM,CAACE,IAAI,CAAC,CAACD,eAAe,CAAC,CAAC;AACvC;;AAEA;AACA,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}