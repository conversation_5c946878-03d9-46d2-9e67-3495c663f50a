import React from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { Box, Typography, useTheme } from '@mui/material';
import { CompanyActivity } from '../services/apiService';

interface TopCompaniesChartProps {
  data: CompanyActivity[];
}

const TopCompaniesChart: React.FC<TopCompaniesChartProps> = ({ data }) => {
  const theme = useTheme();

  const formatCurrency = (value: number) => {
    if (value >= 10000000) {
      return `₹${(value / 10000000).toFixed(1)}Cr`;
    } else if (value >= 100000) {
      return `₹${(value / 100000).toFixed(1)}L`;
    } else {
      return `₹${value.toLocaleString()}`;
    }
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'total_value') {
      return [formatCurrency(value), 'Total Value'];
    }
    return [value, name];
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            backgroundColor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            boxShadow: theme.shadows[4],
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {data.company_name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Symbol: {data.symbol}
          </Typography>
          <Typography variant="body2" color="primary.main" fontWeight={600}>
            Total Value: {formatCurrency(data.total_value)}
          </Typography>
          <Typography variant="body2" color="success.main">
            Buy Value: {formatCurrency(data.total_buy_value)}
          </Typography>
          <Typography variant="body2" color="error.main">
            Sell Value: {formatCurrency(data.total_sell_value)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Transactions: {data.transaction_count}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Unique Insiders: {data.unique_insiders}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  // Prepare chart data
  const chartData = data.slice(0, 10).map((company, index) => ({
    ...company,
    displayName: company.symbol,
    colorIndex: index,
  }));

  // Generate colors
  const colors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.info.main,
    theme.palette.error.main,
    '#9c27b0',
    '#ff5722',
    '#607d8b',
    '#795548',
  ];

  if (!data || data.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <Typography variant="body1" color="text.secondary">
          No data available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: 400 }}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
          <XAxis
            dataKey="displayName"
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            angle={-45}
            textAnchor="end"
            height={80}
            interval={0}
          />
          <YAxis
            tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
            tickFormatter={(value) => {
              if (value >= 10000000) {
                return `₹${(value / 10000000).toFixed(0)}Cr`;
              } else if (value >= 100000) {
                return `₹${(value / 100000).toFixed(0)}L`;
              } else {
                return `₹${(value / 1000).toFixed(0)}K`;
              }
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="total_value"
            radius={[4, 4, 0, 0]}
            stroke={theme.palette.primary.main}
            strokeWidth={1}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default TopCompaniesChart;
