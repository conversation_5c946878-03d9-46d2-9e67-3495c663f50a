"""
Database connection and initialization utilities for NSE Insider Trading System
"""

import os
import asyncio
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
import asyncpg
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2.pool import ThreadedConnectionPool
import json
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = "postgresql://nsescreaper_owner:<EMAIL>/nsescreaper?sslmode=require"

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self, database_url: str = DATABASE_URL):
        self.database_url = database_url
        self.pool: Optional[ThreadedConnectionPool] = None
        self.async_pool: Optional[asyncpg.Pool] = None
        
    def initialize_sync_pool(self, min_conn: int = 1, max_conn: int = 20):
        """Initialize synchronous connection pool"""
        try:
            self.pool = ThreadedConnectionPool(
                min_conn, max_conn, self.database_url
            )
            logger.info(f"Initialized sync connection pool with {min_conn}-{max_conn} connections")
        except Exception as e:
            logger.error(f"Failed to initialize sync connection pool: {e}")
            raise
    
    async def initialize_async_pool(self, min_size: int = 10, max_size: int = 20):
        """Initialize asynchronous connection pool"""
        try:
            self.async_pool = await asyncpg.create_pool(
                self.database_url,
                min_size=min_size,
                max_size=max_size,
                command_timeout=60
            )
            logger.info(f"Initialized async connection pool with {min_size}-{max_size} connections")
        except Exception as e:
            logger.error(f"Failed to initialize async connection pool: {e}")
            raise
    
    @asynccontextmanager
    async def get_async_connection(self):
        """Get async database connection from pool"""
        if not self.async_pool:
            await self.initialize_async_pool()
        
        async with self.async_pool.acquire() as connection:
            yield connection
    
    def get_sync_connection(self):
        """Get sync database connection from pool"""
        if not self.pool:
            self.initialize_sync_pool()
        
        return self.pool.getconn()
    
    def return_sync_connection(self, conn):
        """Return sync connection to pool"""
        if self.pool:
            self.pool.putconn(conn)
    
    async def close_pools(self):
        """Close all connection pools"""
        if self.async_pool:
            await self.async_pool.close()
            logger.info("Closed async connection pool")
        
        if self.pool:
            self.pool.closeall()
            logger.info("Closed sync connection pool")

class DatabaseInitializer:
    """Handles database schema initialization and migrations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def execute_schema_file(self, schema_file_path: str = "database/schema.sql"):
        """Execute SQL schema file"""
        try:
            with open(schema_file_path, 'r') as file:
                schema_sql = file.read()
            
            conn = self.db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute(schema_sql)
                    conn.commit()
                    logger.info("Database schema initialized successfully")
            finally:
                self.db_manager.return_sync_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to initialize database schema: {e}")
            raise
    
    def verify_schema(self) -> bool:
        """Verify that all required tables exist"""
        required_tables = [
            'companies', 'person_categories', 'transaction_types',
            'transaction_modes', 'security_types', 'exchanges',
            'insider_transactions', 'data_quality_log', 
            'scraper_logs', 'api_usage_log'
        ]
        
        try:
            conn = self.db_manager.get_sync_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                    """)
                    existing_tables = [row[0] for row in cursor.fetchall()]
                    
                    missing_tables = set(required_tables) - set(existing_tables)
                    if missing_tables:
                        logger.error(f"Missing tables: {missing_tables}")
                        return False
                    
                    logger.info("All required tables exist")
                    return True
            finally:
                self.db_manager.return_sync_connection(conn)
                
        except Exception as e:
            logger.error(f"Failed to verify schema: {e}")
            return False

class DataAccessLayer:
    """Data access layer for common database operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_or_create_company(self, symbol: str, company_name: str) -> str:
        """Get existing company or create new one, returns company_id"""
        conn = self.db_manager.get_sync_connection()
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Try to get existing company
                cursor.execute(
                    "SELECT id FROM companies WHERE symbol = %s",
                    (symbol,)
                )
                result = cursor.fetchone()
                
                if result:
                    return str(result['id'])
                
                # Create new company
                cursor.execute("""
                    INSERT INTO companies (symbol, company_name)
                    VALUES (%s, %s)
                    RETURNING id
                """, (symbol, company_name))
                
                result = cursor.fetchone()
                conn.commit()
                logger.info(f"Created new company: {symbol} - {company_name}")
                return str(result['id'])
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to get/create company {symbol}: {e}")
            raise
        finally:
            self.db_manager.return_sync_connection(conn)
    
    def get_lookup_id(self, table: str, name_column: str, name_value: str) -> Optional[int]:
        """Get ID from lookup table"""
        conn = self.db_manager.get_sync_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    f"SELECT id FROM {table} WHERE {name_column} = %s",
                    (name_value,)
                )
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get lookup ID from {table}: {e}")
            return None
        finally:
            self.db_manager.return_sync_connection(conn)
    
    def insert_transaction(self, transaction_data: Dict[str, Any]) -> str:
        """Insert insider transaction record"""
        conn = self.db_manager.get_sync_connection()
        try:
            with conn.cursor() as cursor:
                # Check for duplicate based on NSE IDs
                nse_pid = transaction_data.get('nse_pid')
                nse_did = transaction_data.get('nse_did')

                if nse_pid and nse_did and nse_pid != '' and nse_did != '':
                    cursor.execute("""
                        SELECT id FROM insider_transactions
                        WHERE nse_pid = %s AND nse_did = %s
                    """, (nse_pid, nse_did))

                    if cursor.fetchone():
                        logger.debug(f"Duplicate transaction found: PID={nse_pid}, DID={nse_did}")
                        return None
                
                # Insert new transaction
                insert_query = """
                    INSERT INTO insider_transactions (
                        company_id, person_name, person_category_id,
                        transaction_date, intimation_date, acquisition_from_date, acquisition_to_date,
                        transaction_type_id, transaction_mode_id, security_type_id, exchange_id,
                        buy_value, sell_value, buy_quantity, sell_quantity, security_value, securities_acquired,
                        shares_before_transaction, percentage_before_transaction,
                        shares_after_transaction, percentage_after_transaction,
                        derivative_contract_type, remarks, xbrl_link,
                        nse_pid, nse_did, nse_anex, raw_data, data_source
                    ) VALUES (
                        %(company_id)s, %(person_name)s, %(person_category_id)s,
                        %(transaction_date)s, %(intimation_date)s, %(acquisition_from_date)s, %(acquisition_to_date)s,
                        %(transaction_type_id)s, %(transaction_mode_id)s, %(security_type_id)s, %(exchange_id)s,
                        %(buy_value)s, %(sell_value)s, %(buy_quantity)s, %(sell_quantity)s, 
                        %(security_value)s, %(securities_acquired)s,
                        %(shares_before_transaction)s, %(percentage_before_transaction)s,
                        %(shares_after_transaction)s, %(percentage_after_transaction)s,
                        %(derivative_contract_type)s, %(remarks)s, %(xbrl_link)s,
                        %(nse_pid)s, %(nse_did)s, %(nse_anex)s, %(raw_data)s, %(data_source)s
                    ) RETURNING id
                """
                
                cursor.execute(insert_query, transaction_data)
                result = cursor.fetchone()
                conn.commit()
                
                transaction_id = str(result[0])
                logger.debug(f"Inserted transaction: {transaction_id}")
                return transaction_id
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to insert transaction: {e}")
            raise
        finally:
            self.db_manager.return_sync_connection(conn)
    
    def log_scraper_execution(self, execution_data: Dict[str, Any]) -> str:
        """Log scraper execution details"""
        conn = self.db_manager.get_sync_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO scraper_logs (
                        execution_start, execution_end, status,
                        records_fetched, records_inserted, records_updated, records_skipped,
                        error_message, execution_details
                    ) VALUES (
                        %(execution_start)s, %(execution_end)s, %(status)s,
                        %(records_fetched)s, %(records_inserted)s, %(records_updated)s, %(records_skipped)s,
                        %(error_message)s, %(execution_details)s
                    ) RETURNING id
                """, execution_data)
                
                result = cursor.fetchone()
                conn.commit()
                return str(result[0])
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to log scraper execution: {e}")
            raise
        finally:
            self.db_manager.return_sync_connection(conn)

# Global database manager instance
db_manager = DatabaseManager()
data_access = DataAccessLayer(db_manager)

def initialize_database():
    """Initialize database schema and verify setup"""
    try:
        initializer = DatabaseInitializer(db_manager)
        initializer.execute_schema_file()
        
        if initializer.verify_schema():
            logger.info("Database initialization completed successfully")
            return True
        else:
            logger.error("Database schema verification failed")
            return False
            
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        return False

if __name__ == "__main__":
    # Initialize database when run directly
    success = initialize_database()
    if success:
        print("✅ Database initialized successfully")
    else:
        print("❌ Database initialization failed")
