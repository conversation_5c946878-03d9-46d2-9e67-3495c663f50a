{"ast": null, "code": "export { default } from \"./Popper.js\";\nexport * from \"./popperClasses.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/Popper/index.js"], "sourcesContent": ["export { default } from \"./Popper.js\";\nexport * from \"./popperClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}