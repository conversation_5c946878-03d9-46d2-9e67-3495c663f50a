{"ast": null, "code": "export { default } from \"./GridLegacy.js\";\nexport { default as gridLegacyClasses } from \"./gridLegacyClasses.js\";\nexport * from \"./gridLegacyClasses.js\";", "map": {"version": 3, "names": ["default", "gridLegacyClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/GridLegacy/index.js"], "sourcesContent": ["export { default } from \"./GridLegacy.js\";\nexport { default as gridLegacyClasses } from \"./gridLegacyClasses.js\";\nexport * from \"./gridLegacyClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,wBAAwB;AACrE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}