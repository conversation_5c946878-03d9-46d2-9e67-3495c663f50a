{"ast": null, "code": "export { default } from \"./TableFooter.js\";\nexport { default as tableFooterClasses } from \"./tableFooterClasses.js\";\nexport * from \"./tableFooterClasses.js\";", "map": {"version": 3, "names": ["default", "tableFooterClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/TableFooter/index.js"], "sourcesContent": ["export { default } from \"./TableFooter.js\";\nexport { default as tableFooterClasses } from \"./tableFooterClasses.js\";\nexport * from \"./tableFooterClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAC1C,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,cAAc,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}