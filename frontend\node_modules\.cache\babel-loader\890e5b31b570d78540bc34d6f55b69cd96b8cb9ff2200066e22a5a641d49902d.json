{"ast": null, "code": "export { default } from \"./PaginationItem.js\";\nexport { default as paginationItemClasses } from \"./paginationItemClasses.js\";\nexport * from \"./paginationItemClasses.js\";", "map": {"version": 3, "names": ["default", "paginationItemClasses"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/PaginationItem/index.js"], "sourcesContent": ["export { default } from \"./PaginationItem.js\";\nexport { default as paginationItemClasses } from \"./paginationItemClasses.js\";\nexport * from \"./paginationItemClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASA,OAAO,IAAIC,qBAAqB,QAAQ,4BAA4B;AAC7E,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}