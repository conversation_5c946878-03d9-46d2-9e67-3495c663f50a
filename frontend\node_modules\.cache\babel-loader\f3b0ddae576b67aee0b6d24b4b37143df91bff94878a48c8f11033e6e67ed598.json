{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport ThemeProviderNoVars from \"./ThemeProviderNoVars.js\";\nimport { CssVarsProvider } from \"./ThemeProviderWithVars.js\";\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProvider({\n  theme,\n  ...props\n}) {\n  const noVarsTheme = React.useMemo(() => {\n    if (typeof theme === 'function') {\n      return theme;\n    }\n    const muiTheme = THEME_ID in theme ? theme[THEME_ID] : theme;\n    if (!('colorSchemes' in muiTheme)) {\n      if (!('vars' in muiTheme)) {\n        // For non-CSS variables themes, set `vars` to null to prevent theme inheritance from the upper theme.\n        // The example use case is the docs demo that uses ThemeProvider to customize the theme while the upper theme is using CSS variables.\n        return {\n          ...theme,\n          vars: null\n        };\n      }\n      return theme;\n    }\n    return null;\n  }, [theme]);\n  if (noVarsTheme) {\n    return /*#__PURE__*/_jsx(ThemeProviderNoVars, {\n      theme: noVarsTheme,\n      ...props\n    });\n  }\n  return /*#__PURE__*/_jsx(CssVarsProvider, {\n    theme: theme,\n    ...props\n  });\n}", "map": {"version": 3, "names": ["React", "ThemeProviderNoVars", "CssVarsProvider", "THEME_ID", "jsx", "_jsx", "ThemeProvider", "theme", "props", "noVarsTheme", "useMemo", "muiTheme", "vars"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/styles/ThemeProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport ThemeProviderNoVars from \"./ThemeProviderNoVars.js\";\nimport { CssVarsProvider } from \"./ThemeProviderWithVars.js\";\nimport THEME_ID from \"./identifier.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function ThemeProvider({\n  theme,\n  ...props\n}) {\n  const noVarsTheme = React.useMemo(() => {\n    if (typeof theme === 'function') {\n      return theme;\n    }\n    const muiTheme = THEME_ID in theme ? theme[THEME_ID] : theme;\n    if (!('colorSchemes' in muiTheme)) {\n      if (!('vars' in muiTheme)) {\n        // For non-CSS variables themes, set `vars` to null to prevent theme inheritance from the upper theme.\n        // The example use case is the docs demo that uses ThemeProvider to customize the theme while the upper theme is using CSS variables.\n        return {\n          ...theme,\n          vars: null\n        };\n      }\n      return theme;\n    }\n    return null;\n  }, [theme]);\n  if (noVarsTheme) {\n    return /*#__PURE__*/_jsx(ThemeProviderNoVars, {\n      theme: noVarsTheme,\n      ...props\n    });\n  }\n  return /*#__PURE__*/_jsx(CssVarsProvider, {\n    theme: theme,\n    ...props\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAAC;EACpCC,KAAK;EACL,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,WAAW,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM;IACtC,IAAI,OAAOH,KAAK,KAAK,UAAU,EAAE;MAC/B,OAAOA,KAAK;IACd;IACA,MAAMI,QAAQ,GAAGR,QAAQ,IAAII,KAAK,GAAGA,KAAK,CAACJ,QAAQ,CAAC,GAAGI,KAAK;IAC5D,IAAI,EAAE,cAAc,IAAII,QAAQ,CAAC,EAAE;MACjC,IAAI,EAAE,MAAM,IAAIA,QAAQ,CAAC,EAAE;QACzB;QACA;QACA,OAAO;UACL,GAAGJ,KAAK;UACRK,IAAI,EAAE;QACR,CAAC;MACH;MACA,OAAOL,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,IAAIE,WAAW,EAAE;IACf,OAAO,aAAaJ,IAAI,CAACJ,mBAAmB,EAAE;MAC5CM,KAAK,EAAEE,WAAW;MAClB,GAAGD;IACL,CAAC,CAAC;EACJ;EACA,OAAO,aAAaH,IAAI,CAACH,eAAe,EAAE;IACxCK,KAAK,EAAEA,KAAK;IACZ,GAAGC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}