"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.gridSortedRowIndexLookupSelector = exports.gridSortedRowIdsSelector = exports.gridSortedRowEntriesSelector = exports.gridSortModelSelector = exports.gridSortColumnLookupSelector = void 0;
var _createSelector = require("../../../utils/createSelector");
var _gridRowsSelector = require("../rows/gridRowsSelector");
var _gridRowsUtils = require("../rows/gridRowsUtils");
/**
 * @category Sorting
 * @ignore - do not document.
 */
const gridSortingStateSelector = (0, _createSelector.createRootSelector)(state => state.sorting);

/**
 * Get the id of the rows after the sorting process.
 * @category Sorting
 */
const gridSortedRowIdsSelector = exports.gridSortedRowIdsSelector = (0, _createSelector.createSelector)(gridSortingStateSelector, sortingState => sortingState.sortedRows);

/**
 * Get the id and the model of the rows after the sorting process.
 * @category Sorting
 */
const gridSortedRowEntriesSelector = exports.gridSortedRowEntriesSelector = (0, _createSelector.createSelectorMemoized)(gridSortedRowIdsSelector, _gridRowsSelector.gridRowsLookupSelector, _gridRowsSelector.gridRowTreeSelector, (sortedIds, idRowsLookup, rowTree) => sortedIds.reduce((acc, id) => {
  const model = idRowsLookup[id];
  if (model) {
    acc.push({
      id,
      model
    });
  } else {
    const rowNode = rowTree[id];
    if (rowNode && (0, _gridRowsUtils.isAutogeneratedRowNode)(rowNode)) {
      acc.push({
        id,
        model: {
          [_gridRowsUtils.GRID_ID_AUTOGENERATED]: id
        }
      });
    }
  }
  return acc;
}, []));

/**
 * Get the current sorting model.
 * @category Sorting
 */
const gridSortModelSelector = exports.gridSortModelSelector = (0, _createSelector.createSelector)(gridSortingStateSelector, sorting => sorting.sortModel);
/**
 * @category Sorting
 * @ignore - do not document.
 */
const gridSortColumnLookupSelector = exports.gridSortColumnLookupSelector = (0, _createSelector.createSelectorMemoized)(gridSortModelSelector, sortModel => {
  const result = sortModel.reduce((res, sortItem, index) => {
    res[sortItem.field] = {
      sortDirection: sortItem.sort,
      sortIndex: sortModel.length > 1 ? index + 1 : undefined
    };
    return res;
  }, {});
  return result;
});

/**
 * @category Sorting
 * @ignore - do not document.
 */
const gridSortedRowIndexLookupSelector = exports.gridSortedRowIndexLookupSelector = (0, _createSelector.createSelectorMemoized)(gridSortedRowIdsSelector, sortedIds => {
  return sortedIds.reduce((acc, id, index) => {
    acc[id] = index;
    return acc;
  }, Object.create(null));
});