Metadata-Version: 2.1
Name: asyncpg
Version: 0.30.0
Summary: An asyncio PostgreSQL driver
Author-email: MagicStack Inc <<EMAIL>>
License: Apache License, Version 2.0
Project-URL: github, https://github.com/MagicStack/asyncpg
Keywords: database,postgres
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: AsyncIO
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Database :: Front-Ends
Requires-Python: >=3.8.0
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: AUTHORS
Requires-Dist: async-timeout>=4.0.3; python_version < "3.11.0"
Provides-Extra: docs
Requires-Dist: Sphinx~=8.1.3; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.2.2; extra == "docs"
Provides-Extra: gssauth
Requires-Dist: gssapi; platform_system != "Windows" and extra == "gssauth"
Requires-Dist: sspilib; platform_system == "Windows" and extra == "gssauth"
Provides-Extra: test
Requires-Dist: flake8~=6.1; extra == "test"
Requires-Dist: flake8-pyi~=24.1.0; extra == "test"
Requires-Dist: distro~=1.9.0; extra == "test"
Requires-Dist: mypy~=1.8.0; extra == "test"
Requires-Dist: uvloop>=0.15.3; (platform_system != "Windows" and python_version < "3.14.0") and extra == "test"
Requires-Dist: gssapi; platform_system == "Linux" and extra == "test"
Requires-Dist: k5test; platform_system == "Linux" and extra == "test"
Requires-Dist: sspilib; platform_system == "Windows" and extra == "test"

asyncpg -- A fast PostgreSQL Database Client Library for Python/asyncio
=======================================================================

.. image:: https://github.com/MagicStack/asyncpg/workflows/Tests/badge.svg
   :target: https://github.com/MagicStack/asyncpg/actions?query=workflow%3ATests+branch%3Amaster
   :alt: GitHub Actions status
.. image:: https://img.shields.io/pypi/v/asyncpg.svg
   :target: https://pypi.python.org/pypi/asyncpg

**asyncpg** is a database interface library designed specifically for
PostgreSQL and Python/asyncio.  asyncpg is an efficient, clean implementation
of PostgreSQL server binary protocol for use with Python's ``asyncio``
framework.  You can read more about asyncpg in an introductory
`blog post <http://magic.io/blog/asyncpg-1m-rows-from-postgres-to-python/>`_.

asyncpg requires Python 3.8 or later and is supported for PostgreSQL
versions 9.5 to 17.  Other PostgreSQL versions or other databases
implementing the PostgreSQL protocol *may* work, but are not being
actively tested.


Documentation
-------------

The project documentation can be found
`here <https://magicstack.github.io/asyncpg/current/>`_.


Performance
-----------

In our testing asyncpg is, on average, **5x** faster than psycopg3.

.. image:: https://raw.githubusercontent.com/MagicStack/asyncpg/master/performance.png?fddca40ab0
    :target: https://gistpreview.github.io/?0ed296e93523831ea0918d42dd1258c2

The above results are a geometric mean of benchmarks obtained with PostgreSQL
`client driver benchmarking toolbench <https://github.com/MagicStack/pgbench>`_
in June 2023 (click on the chart to see full details).


Features
--------

asyncpg implements PostgreSQL server protocol natively and exposes its
features directly, as opposed to hiding them behind a generic facade
like DB-API.

This enables asyncpg to have easy-to-use support for:

* **prepared statements**
* **scrollable cursors**
* **partial iteration** on query results
* automatic encoding and decoding of composite types, arrays,
  and any combination of those
* straightforward support for custom data types


Installation
------------

asyncpg is available on PyPI.  When not using GSSAPI/SSPI authentication it
has no dependencies.  Use pip to install::

    $ pip install asyncpg

If you need GSSAPI/SSPI authentication, use::

    $ pip install 'asyncpg[gssauth]'

For more details, please `see the documentation
<https://magicstack.github.io/asyncpg/current/installation.html>`_.


Basic Usage
-----------

.. code-block:: python

    import asyncio
    import asyncpg

    async def run():
        conn = await asyncpg.connect(user='user', password='password',
                                     database='database', host='127.0.0.1')
        values = await conn.fetch(
            'SELECT * FROM mytable WHERE id = $1',
            10,
        )
        await conn.close()

    asyncio.run(run())


License
-------

asyncpg is developed and distributed under the Apache 2.0 license.
