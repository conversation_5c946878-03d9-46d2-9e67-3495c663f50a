-- NSE Insider Trading Database Schema
-- Optimized for time-series queries and analytics

-- Create database extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Companies table
CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL UNIQUE,
    company_name VARCHAR(500) NOT NULL,
    sector VARCHAR(100),
    industry VARCHAR(100),
    market_cap BIGINT,
    listing_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Person categories lookup
CREATE TABLE IF NOT EXISTS person_categories (
    id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Transaction types lookup
CREATE TABLE IF NOT EXISTS transaction_types (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_buy BOOLEAN NOT NULL DEFAULT FALSE,
    is_sell BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Transaction modes lookup
CREATE TABLE IF NOT EXISTS transaction_modes (
    id SERIAL PRIMARY KEY,
    mode_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Security types lookup
CREATE TABLE IF NOT EXISTS security_types (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Exchanges lookup
CREATE TABLE IF NOT EXISTS exchanges (
    id SERIAL PRIMARY KEY,
    exchange_name VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(200),
    country VARCHAR(50) DEFAULT 'India',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Main insider trading transactions table
CREATE TABLE IF NOT EXISTS insider_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Company and person information
    company_id UUID NOT NULL REFERENCES companies(id),
    person_name VARCHAR(500) NOT NULL,
    person_category_id INTEGER REFERENCES person_categories(id),
    
    -- Transaction details
    transaction_date TIMESTAMP WITH TIME ZONE NOT NULL,
    intimation_date TIMESTAMP WITH TIME ZONE,
    acquisition_from_date TIMESTAMP WITH TIME ZONE,
    acquisition_to_date TIMESTAMP WITH TIME ZONE,
    
    -- Transaction classification
    transaction_type_id INTEGER REFERENCES transaction_types(id),
    transaction_mode_id INTEGER REFERENCES transaction_modes(id),
    security_type_id INTEGER REFERENCES security_types(id),
    exchange_id INTEGER REFERENCES exchanges(id),
    
    -- Financial data
    buy_value DECIMAL(20,2),
    sell_value DECIMAL(20,2),
    buy_quantity BIGINT,
    sell_quantity BIGINT,
    security_value DECIMAL(20,2),
    securities_acquired BIGINT,
    
    -- Holdings information
    shares_before_transaction BIGINT,
    percentage_before_transaction DECIMAL(8,4),
    shares_after_transaction BIGINT,
    percentage_after_transaction DECIMAL(8,4),
    
    -- Additional fields
    derivative_contract_type VARCHAR(100),
    remarks TEXT,
    xbrl_link TEXT,
    
    -- NSE specific identifiers
    nse_pid VARCHAR(50),
    nse_did VARCHAR(50),
    nse_anex VARCHAR(20),
    
    -- Data management
    raw_data JSONB, -- Store original scraped data
    data_source VARCHAR(50) DEFAULT 'NSE_API',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_transaction_amounts CHECK (
        (buy_value IS NULL OR buy_value >= 0) AND 
        (sell_value IS NULL OR sell_value >= 0) AND
        (security_value IS NULL OR security_value >= 0)
    ),
    CONSTRAINT valid_quantities CHECK (
        (buy_quantity IS NULL OR buy_quantity >= 0) AND 
        (sell_quantity IS NULL OR sell_quantity >= 0) AND
        (securities_acquired IS NULL OR securities_acquired != 0)
    ),
    CONSTRAINT valid_percentages CHECK (
        (percentage_before_transaction IS NULL OR 
         (percentage_before_transaction >= 0 AND percentage_before_transaction <= 100)) AND
        (percentage_after_transaction IS NULL OR 
         (percentage_after_transaction >= 0 AND percentage_after_transaction <= 100))
    )
);

-- Data quality and deduplication table
CREATE TABLE IF NOT EXISTS data_quality_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID REFERENCES insider_transactions(id),
    quality_check_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL, -- PASS, FAIL, WARNING
    message TEXT,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Scraper execution log
CREATE TABLE IF NOT EXISTS scraper_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_start TIMESTAMP WITH TIME ZONE NOT NULL,
    execution_end TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL, -- RUNNING, SUCCESS, FAILED, PARTIAL
    records_fetched INTEGER DEFAULT 0,
    records_inserted INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_skipped INTEGER DEFAULT 0,
    error_message TEXT,
    execution_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- API usage tracking
CREATE TABLE IF NOT EXISTS api_usage_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    endpoint VARCHAR(200) NOT NULL,
    method VARCHAR(10) NOT NULL,
    user_id VARCHAR(100),
    ip_address INET,
    response_status INTEGER,
    response_time_ms INTEGER,
    request_size INTEGER,
    response_size INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for optimal query performance
-- Time-series queries
CREATE INDEX IF NOT EXISTS idx_insider_transactions_date ON insider_transactions(transaction_date DESC);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_company_date ON insider_transactions(company_id, transaction_date DESC);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_person_date ON insider_transactions(person_name, transaction_date DESC);

-- Company lookups
CREATE INDEX IF NOT EXISTS idx_insider_transactions_company ON insider_transactions(company_id);
CREATE INDEX IF NOT EXISTS idx_companies_symbol ON companies(symbol);
CREATE INDEX IF NOT EXISTS idx_companies_name_trgm ON companies USING gin(company_name gin_trgm_ops);

-- Person and category searches
CREATE INDEX IF NOT EXISTS idx_insider_transactions_person ON insider_transactions(person_name);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_person_trgm ON insider_transactions USING gin(person_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_category ON insider_transactions(person_category_id);

-- Transaction analysis
CREATE INDEX IF NOT EXISTS idx_insider_transactions_type ON insider_transactions(transaction_type_id);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_mode ON insider_transactions(transaction_mode_id);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_value ON insider_transactions(security_value DESC) WHERE security_value IS NOT NULL;

-- Deduplication and data quality
CREATE INDEX IF NOT EXISTS idx_insider_transactions_nse_ids ON insider_transactions(nse_pid, nse_did);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_raw_data ON insider_transactions USING gin(raw_data);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_insider_transactions_company_type_date ON insider_transactions(company_id, transaction_type_id, transaction_date DESC);
CREATE INDEX IF NOT EXISTS idx_insider_transactions_category_date_value ON insider_transactions(person_category_id, transaction_date DESC, security_value DESC);

-- Monitoring and logging indexes
CREATE INDEX IF NOT EXISTS idx_scraper_logs_date ON scraper_logs(execution_start DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_date ON api_usage_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage_log(endpoint, created_at DESC);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_insider_transactions_updated_at BEFORE UPDATE ON insider_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial lookup data
INSERT INTO person_categories (category_name, description) VALUES
    ('Promoters', 'Company promoters and founding members'),
    ('Promoter Group', 'Entities associated with promoters'),
    ('Director', 'Board of directors'),
    ('Key Managerial Personnel', 'Senior management personnel'),
    ('Employees/Designated Employees', 'Company employees with access to insider information'),
    ('Immediate relative', 'Family members of insiders'),
    ('Other', 'Other categories of insiders')
ON CONFLICT (category_name) DO NOTHING;

INSERT INTO transaction_types (type_name, description, is_buy, is_sell) VALUES
    ('Buy', 'Purchase of securities', TRUE, FALSE),
    ('Sell', 'Sale of securities', FALSE, TRUE),
    ('Pledge', 'Pledging of securities', FALSE, FALSE),
    ('Pledge Revoke', 'Revocation of pledge', FALSE, FALSE),
    ('Gift', 'Transfer as gift', FALSE, FALSE),
    ('ESOP', 'Employee Stock Option Plan exercise', TRUE, FALSE),
    ('Conversion', 'Conversion of securities', FALSE, FALSE),
    ('Others', 'Other types of transactions', FALSE, FALSE)
ON CONFLICT (type_name) DO NOTHING;

INSERT INTO transaction_modes (mode_name, description) VALUES
    ('Market Purchase', 'Purchase through stock exchange'),
    ('Market Sale', 'Sale through stock exchange'),
    ('Off Market', 'Off-market transaction'),
    ('Inter-se-Transfer', 'Transfer between related parties'),
    ('ESOP', 'Employee stock option exercise'),
    ('Gift', 'Gift transaction'),
    ('Conversion of security', 'Security conversion'),
    ('Revokation of Pledge', 'Pledge revocation'),
    ('Pledge Creation', 'Creation of pledge'),
    ('Others', 'Other transaction modes')
ON CONFLICT (mode_name) DO NOTHING;

INSERT INTO security_types (type_name, description) VALUES
    ('Equity Shares', 'Common equity shares'),
    ('Preference Shares', 'Preference shares'),
    ('Warrants', 'Share warrants'),
    ('Convertible Securities', 'Convertible bonds/debentures'),
    ('ADR/GDR/FCCB', 'American/Global Depositary Receipts or Foreign Currency Convertible Bonds'),
    ('Derivatives', 'Derivative instruments'),
    ('Others', 'Other security types')
ON CONFLICT (type_name) DO NOTHING;

INSERT INTO exchanges (exchange_name, full_name) VALUES
    ('NSE', 'National Stock Exchange of India'),
    ('BSE', 'Bombay Stock Exchange'),
    ('MSEI', 'Metropolitan Stock Exchange of India'),
    ('NA', 'Not Applicable')
ON CONFLICT (exchange_name) DO NOTHING;
