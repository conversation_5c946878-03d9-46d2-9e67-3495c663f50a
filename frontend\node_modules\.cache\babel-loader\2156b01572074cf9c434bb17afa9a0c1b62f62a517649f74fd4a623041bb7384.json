{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { globalCss } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\n\n// to determine if the global styles are static or dynamic\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isDynamicSupport = typeof globalCss({}) === 'function';\nexport const html = (theme, enableColorScheme) => ({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%',\n  // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n  ...(enableColorScheme && !theme.vars && {\n    colorScheme: theme.palette.mode\n  })\n});\nexport const body = theme => ({\n  color: (theme.vars || theme).palette.text.primary,\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        colorSchemeStyles[selector] = {\n          ':root': {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  let defaultStyles = {\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: {\n      margin: 0,\n      // Remove the margin in all browsers.\n      ...body(theme),\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    },\n    ...colorSchemeStyles\n  };\n  const themeOverrides = theme.components?.MuiCssBaseline?.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = theme => {\n  const result = styles(theme, false);\n  const baseStyles = Array.isArray(result) ? result[0] : result;\n  if (!theme.vars && baseStyles) {\n    baseStyles.html[`:root:has(${SELECTOR})`] = {\n      colorScheme: theme.palette.mode\n    };\n  }\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        baseStyles[selector] = {\n          [`:root:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        baseStyles[selector.replace(/\\s*&/, '')] = {\n          [`&:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      }\n    });\n  }\n  return result;\n};\nconst GlobalStyles = globalCss(isDynamicSupport ? ({\n  theme,\n  enableColorScheme\n}) => styles(theme, enableColorScheme) : ({\n  theme\n}) => staticStyles(theme));\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [isDynamicSupport && /*#__PURE__*/_jsx(GlobalStyles, {\n      enableColorScheme: enableColorScheme\n    }), !isDynamicSupport && !enableColorScheme && /*#__PURE__*/_jsx(\"span\", {\n      className: SELECTOR,\n      style: {\n        display: 'none'\n      }\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;", "map": {"version": 3, "names": ["React", "PropTypes", "globalCss", "useDefaultProps", "jsx", "_jsx", "jsxs", "_jsxs", "isDynamicSupport", "html", "theme", "enableColorScheme", "WebkitFontSmoothing", "MozOsxFontSmoothing", "boxSizing", "WebkitTextSizeAdjust", "vars", "colorScheme", "palette", "mode", "body", "color", "text", "primary", "typography", "body1", "backgroundColor", "background", "default", "common", "white", "styles", "colorSchemeStyles", "colorSchemes", "getColorSchemeSelector", "Object", "entries", "for<PERSON>ach", "key", "scheme", "selector", "startsWith", "replace", "defaultStyles", "fontWeight", "fontWeightBold", "margin", "themeOverrides", "components", "MuiCssBaseline", "styleOverrides", "SELECTOR", "staticStyles", "result", "baseStyles", "Array", "isArray", "GlobalStyles", "CssBaseline", "inProps", "props", "name", "children", "Fragment", "className", "style", "display", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["D:/chirag/nsescrapper/frontend/node_modules/@mui/material/esm/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { globalCss } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\n\n// to determine if the global styles are static or dynamic\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst isDynamicSupport = typeof globalCss({}) === 'function';\nexport const html = (theme, enableColorScheme) => ({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%',\n  // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n  ...(enableColorScheme && !theme.vars && {\n    colorScheme: theme.palette.mode\n  })\n});\nexport const body = theme => ({\n  color: (theme.vars || theme).palette.text.primary,\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        colorSchemeStyles[selector] = {\n          ':root': {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  let defaultStyles = {\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: {\n      margin: 0,\n      // Remove the margin in all browsers.\n      ...body(theme),\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    },\n    ...colorSchemeStyles\n  };\n  const themeOverrides = theme.components?.MuiCssBaseline?.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = theme => {\n  const result = styles(theme, false);\n  const baseStyles = Array.isArray(result) ? result[0] : result;\n  if (!theme.vars && baseStyles) {\n    baseStyles.html[`:root:has(${SELECTOR})`] = {\n      colorScheme: theme.palette.mode\n    };\n  }\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        // for @media (prefers-color-scheme), we need to target :root\n        baseStyles[selector] = {\n          [`:root:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      } else {\n        // else, it's likely that the selector already target an element with a class or data attribute\n        baseStyles[selector.replace(/\\s*&/, '')] = {\n          [`&:not(:has(.${SELECTOR}))`]: {\n            colorScheme: scheme.palette?.mode\n          }\n        };\n      }\n    });\n  }\n  return result;\n};\nconst GlobalStyles = globalCss(isDynamicSupport ? ({\n  theme,\n  enableColorScheme\n}) => styles(theme, enableColorScheme) : ({\n  theme\n}) => staticStyles(theme));\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [isDynamicSupport && /*#__PURE__*/_jsx(GlobalStyles, {\n      enableColorScheme: enableColorScheme\n    }), !isDynamicSupport && !enableColorScheme && /*#__PURE__*/_jsx(\"span\", {\n      className: SELECTOR,\n      style: {\n        display: 'none'\n      }\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,eAAe,QAAQ,kCAAkC;;AAElE;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,gBAAgB,GAAG,OAAON,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,UAAU;AAC5D,OAAO,MAAMO,IAAI,GAAGA,CAACC,KAAK,EAAEC,iBAAiB,MAAM;EACjDC,mBAAmB,EAAE,aAAa;EAClC;EACAC,mBAAmB,EAAE,WAAW;EAChC;EACA;EACA;EACAC,SAAS,EAAE,YAAY;EACvB;EACAC,oBAAoB,EAAE,MAAM;EAC5B;EACA,IAAIJ,iBAAiB,IAAI,CAACD,KAAK,CAACM,IAAI,IAAI;IACtCC,WAAW,EAAEP,KAAK,CAACQ,OAAO,CAACC;EAC7B,CAAC;AACH,CAAC,CAAC;AACF,OAAO,MAAMC,IAAI,GAAGV,KAAK,KAAK;EAC5BW,KAAK,EAAE,CAACX,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACI,IAAI,CAACC,OAAO;EACjD,GAAGb,KAAK,CAACc,UAAU,CAACC,KAAK;EACzBC,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC,OAAO;EACjE,cAAc,EAAE;IACd;IACAF,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACW,MAAM,CAACC;EACxD;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,MAAM,GAAGA,CAACrB,KAAK,EAAEC,iBAAiB,GAAG,KAAK,KAAK;EAC1D,MAAMqB,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAIrB,iBAAiB,IAAID,KAAK,CAACuB,YAAY,IAAI,OAAOvB,KAAK,CAACwB,sBAAsB,KAAK,UAAU,EAAE;IACjGC,MAAM,CAACC,OAAO,CAAC1B,KAAK,CAACuB,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;MAC5D,MAAMC,QAAQ,GAAG9B,KAAK,CAACwB,sBAAsB,CAACI,GAAG,CAAC;MAClD,IAAIE,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC5B;QACAT,iBAAiB,CAACQ,QAAQ,CAAC,GAAG;UAC5B,OAAO,EAAE;YACPvB,WAAW,EAAEsB,MAAM,CAACrB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL;QACAa,iBAAiB,CAACQ,QAAQ,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;UAChDzB,WAAW,EAAEsB,MAAM,CAACrB,OAAO,EAAEC;QAC/B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,IAAIwB,aAAa,GAAG;IAClBlC,IAAI,EAAEA,IAAI,CAACC,KAAK,EAAEC,iBAAiB,CAAC;IACpC,wBAAwB,EAAE;MACxBG,SAAS,EAAE;IACb,CAAC;IACD,WAAW,EAAE;MACX8B,UAAU,EAAElC,KAAK,CAACc,UAAU,CAACqB;IAC/B,CAAC;IACDzB,IAAI,EAAE;MACJ0B,MAAM,EAAE,CAAC;MACT;MACA,GAAG1B,IAAI,CAACV,KAAK,CAAC;MACd;MACA;MACA,aAAa,EAAE;QACbgB,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC;MAC5D;IACF,CAAC;IACD,GAAGI;EACL,CAAC;EACD,MAAMe,cAAc,GAAGrC,KAAK,CAACsC,UAAU,EAAEC,cAAc,EAAEC,cAAc;EACvE,IAAIH,cAAc,EAAE;IAClBJ,aAAa,GAAG,CAACA,aAAa,EAAEI,cAAc,CAAC;EACjD;EACA,OAAOJ,aAAa;AACtB,CAAC;;AAED;AACA,MAAMQ,QAAQ,GAAG,SAAS;AAC1B,MAAMC,YAAY,GAAG1C,KAAK,IAAI;EAC5B,MAAM2C,MAAM,GAAGtB,MAAM,CAACrB,KAAK,EAAE,KAAK,CAAC;EACnC,MAAM4C,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;EAC7D,IAAI,CAAC3C,KAAK,CAACM,IAAI,IAAIsC,UAAU,EAAE;IAC7BA,UAAU,CAAC7C,IAAI,CAAC,aAAa0C,QAAQ,GAAG,CAAC,GAAG;MAC1ClC,WAAW,EAAEP,KAAK,CAACQ,OAAO,CAACC;IAC7B,CAAC;EACH;EACA,IAAIT,KAAK,CAACuB,YAAY,EAAE;IACtBE,MAAM,CAACC,OAAO,CAAC1B,KAAK,CAACuB,YAAY,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;MAC5D,MAAMC,QAAQ,GAAG9B,KAAK,CAACwB,sBAAsB,CAACI,GAAG,CAAC;MAClD,IAAIE,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC5B;QACAa,UAAU,CAACd,QAAQ,CAAC,GAAG;UACrB,CAAC,mBAAmBW,QAAQ,IAAI,GAAG;YACjClC,WAAW,EAAEsB,MAAM,CAACrB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL;QACAmC,UAAU,CAACd,QAAQ,CAACE,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;UACzC,CAAC,eAAeS,QAAQ,IAAI,GAAG;YAC7BlC,WAAW,EAAEsB,MAAM,CAACrB,OAAO,EAAEC;UAC/B;QACF,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAOkC,MAAM;AACf,CAAC;AACD,MAAMI,YAAY,GAAGvD,SAAS,CAACM,gBAAgB,GAAG,CAAC;EACjDE,KAAK;EACLC;AACF,CAAC,KAAKoB,MAAM,CAACrB,KAAK,EAAEC,iBAAiB,CAAC,GAAG,CAAC;EACxCD;AACF,CAAC,KAAK0C,YAAY,CAAC1C,KAAK,CAAC,CAAC;;AAE1B;AACA;AACA;AACA,SAASgD,WAAWA,CAACC,OAAO,EAAE;EAC5B,MAAMC,KAAK,GAAGzD,eAAe,CAAC;IAC5ByD,KAAK,EAAED,OAAO;IACdE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJC,QAAQ;IACRnD,iBAAiB,GAAG;EACtB,CAAC,GAAGiD,KAAK;EACT,OAAO,aAAarD,KAAK,CAACP,KAAK,CAAC+D,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAACtD,gBAAgB,IAAI,aAAaH,IAAI,CAACoD,YAAY,EAAE;MAC7D9C,iBAAiB,EAAEA;IACrB,CAAC,CAAC,EAAE,CAACH,gBAAgB,IAAI,CAACG,iBAAiB,IAAI,aAAaN,IAAI,CAAC,MAAM,EAAE;MACvE2D,SAAS,EAAEb,QAAQ;MACnBc,KAAK,EAAE;QACLC,OAAO,EAAE;MACX;IACF,CAAC,CAAC,EAAEJ,QAAQ;EACd,CAAC,CAAC;AACJ;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,WAAW,CAACY,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACER,QAAQ,EAAE7D,SAAS,CAACsE,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACE5D,iBAAiB,EAAEV,SAAS,CAACuE;AAC/B,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}